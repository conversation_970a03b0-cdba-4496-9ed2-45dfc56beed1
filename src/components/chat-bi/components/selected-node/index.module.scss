.selected {
  position: relative;
  display: flex;
  width: fit-content;
  min-width: 0;
  box-sizing: border-box;
  align-items: center;
  cursor: pointer;
  white-space: nowrap;

  .text {
    margin-left: 4px;
  }

  .tags {
    min-width: 0;
    flex: 1;

    .svg {
      svg {
        position: relative;
        top: 1px;
        width: 16px;
        height: 16px;
        margin: 0 5px;

        .shape-body {
          .fill-dark {
            fill: yellow;
          }
        }
      }
    }

    .node {
      span {
        position: relative;
        white-space: nowrap !important;

        svg {
          g {
            .shape-body {
              .fill-dark {
                fill: yellow;
              }
            }
          }
        }
      }
    }

    span {
      position: relative;
      white-space: nowrap !important;
    }

    .tag {
      margin: 0;
      margin-right: 5px;

      .selected-node-tag {
        margin: 0 !important;

        :global {
          .sdk-ai-bi-for-inspection-icon {
            bottom: 3px;
          }
        }
      }
    
      &:last-child {
        margin-right: 0 !important;
      }
    }

    .selected-node-tag-span {
      display: inline-block;
      overflow: hidden;
      max-width: 250px;
      line-height: 21px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}