/* eslint-disable react/jsx-closing-tag-location */
import React from 'react';
import { Tag } from '@tencent/tea-component';
import { useDispatch } from 'react-redux';
import { changeCommonData } from '@src/store/app-common';
import { LocationIcon } from '@tencent/cloud-chat-ui/dist/kits/icons';
import s from './index.module.scss';

interface SelectedNodesProps {
  selectedNode: string;
  apis: AppPluginAPI.PluginAPI;
}
const SelectedNodes = (props: SelectedNodesProps) => {
  const { selectedNode, apis } = props;
  const dispatch = useDispatch();

  const getNodesInfo = () => {
    const nodes = [];
    apis?.archInfo?.nodeList?.forEach((item) => {
      if (selectedNode === item.DiagramId) {
        apis?.removeAllNodeClass?.();
        apis?.addNodeClass(selectedNode, 'color-node');
        const node = document.getElementById(item.DiagramId);
        const svgElement = node.querySelector('svg');
        const serializer = new XMLSerializer();
        const svgString = serializer.serializeToString(svgElement);
        nodes.push({
          key: item.DiagramId,
          name: item.NodeName,
          svg: svgString,
        });
      }
    });
    return nodes;
  };

  const handleDelSelected = () => {
    dispatch(changeCommonData({
      selectedNode: '',
    }));
  };
  return (
    <div className={s.selected}>
      <LocationIcon />
      <span className={s.text}> 查询范围：</span>
      <div className={s.tags}>
        {
          getNodesInfo()?.length
            ? getNodesInfo()?.map((item) => <span
                className={`${s.tag} ${s.node}`}
                key={item.key}
            >
              <Tag
                className={s['selected-node-tag']}
                onClose={() => handleDelSelected()}
              >
                <span className={s['selected-node-tag-span']} title={item?.name}>
                  <span
                    dangerouslySetInnerHTML={{ __html: item.svg }}
                    className={s.svg}
                  />
                  {item.name}
                </span>
              </Tag>
            </span>)
            : <span
                className={s.tag}
            >
              <Tag
                className={s['selected-node-tag']}
              >
                整个架构图
              </Tag>
            </span>
        }
      </div>
    </div>
  );
};

export default SelectedNodes;
