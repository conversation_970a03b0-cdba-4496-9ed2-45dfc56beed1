import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { a11yDark as dark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import React from 'react';
import { Icon } from '@tencent/tea-component';
import { copyTextToClipboard } from '@src/utils/common-funcs';
import MermaidRender from '../mermaid-render';

/**
 * Code渲染组件,高亮代码
 * @param props
 * @returns
 * <AUTHOR>
 */
const CodeRender = ({
  node, inline, className, children, finish, ...props
}: any): React.ReactElement => {
  // 获取指定的编程语言,默认为sql
  const match = /language-(\w+)/.exec(className || '') ?? [];
  const finalSql = String(children).trim();

  if (match[1] === 'mermaid' && finish) {
    return <MermaidRender code={finalSql} />;
  }

  // inline为``单引号包裹的代码
  return !inline && match ? (
    <div style={{
      position: 'relative',
    }}
    >
      <Icon
        type="copy"
        style={{
          position: 'absolute',
          right: 10,
          top: 10,
          cursor: 'pointer',
        }}
        onClick={() => copyTextToClipboard(finalSql)}
      />
      <SyntaxHighlighter
        {...props}
        style={dark}
        language={match[1] ?? 'sql'}
        PreTag="div"
      >
        {finalSql}
      </SyntaxHighlighter>
    </div>
  ) : (
    <code {...props} className={className}>
      {children}
    </code>
  );
};

export default CodeRender;
