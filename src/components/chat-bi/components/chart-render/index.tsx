/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
import {
  Icon, TabPanel, Card, Tabs,
} from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import ReactEcharts from 'echarts-for-react';
import { DashboardItemValid } from '@src/contants';
import ErrorBoundary from '@src/components/error-boundary';
import { convertToMarkdownTable, getBracketContent, isNumber } from '../../utils';
import CodeRender from '../coder-render';
import s from './index.module.scss';

interface ChartRenderProps {
  chartData: any;
  sql: string;
  changeLoading: boolean;
  options: any;
  width?: number;
  height?: number;
  addon?: React.ReactNode;
  nodeStatus?: DashboardItemValid;
}
const ChartRender = (props: ChartRenderProps) => {
  const {
    chartData, sql, changeLoading, options, width, height, addon, nodeStatus,
  } = props;
  const dimensionsLength = chartData?.dimensions?.length;
  const [active, setActive] = useState(dimensionsLength > 1 ? 'line' : 'table');
  const [chartOption, setChartOption] = useState(options);

  const isTable = useMemo(() => {
    const newOption = { ...options };
    return newOption.series?.some((s) => s?.type === 'table');
  }, [options]);

  useEffect(() => {
    // chartData变化需要修改option
    const unit = getBracketContent(chartData?.dimensions?.[1] ?? '') || '';

    const valueFormatter = (v) => {
      if (!isNumber(v)) {
        return v;
      }
      return `${parseFloat(v.toFixed(2))} ${unit}`;
    };
    const newOption = { ...options };
    if (newOption?.tooltip) {
      newOption.tooltip.valueFormatter = valueFormatter;
    }
    newOption.series?.forEach((s) => {
      if (s?.type && s?.type !== 'pie') {
        s.encode = {
          x: 0,
          y: s?.name,
        };
      }
    });
    newOption.dataset = chartData;
    setChartOption(newOption);
  }, [chartData, options]);

  useEffect(() => {
    if (isTable) {
      setActive('table');
      return;
    }
    const dimensionsLength = chartData?.dimensions?.length;
    if (dimensionsLength > 1 || nodeStatus === DashboardItemValid.invalid) {
      setActive('line');
    }
  }, [chartData, isTable]);

  const realChartData = Object.keys(chartData ?? {})?.length > 0 ? chartData : { dimensions: [], source: [] };

  const tabs = nodeStatus === DashboardItemValid.invalid
    ? [{ id: 'line', label: <Icon type="chart-line" /> }]
    : dimensionsLength > 1 && !isTable
      ? [
        { id: 'line', label: <Icon type="chart-line" /> },
        { id: 'table', label: <Icon type="datasheet" /> },
        { id: 'sql', label: <span style={{ color: 'rgb(99 93 93)', fontWeight: 500 }}>SQL</span> },
      ] : [{ id: 'table', label: <Icon type="datasheet" /> },
        { id: 'sql', label: <span style={{ color: 'rgb(99 93 93)', fontWeight: 500 }}>SQL</span> }];
  const hasData = Object.keys(chartData).length > 0;

  return hasData ? (
    <Card
      className={active !== 'line' ? `${s.common} ${s.bi}` : s.common}
      style={{ marginTop: 10, width: width ?? '', height: height ?? '' }}
    >
      <Tabs
        tabs={tabs}
        destroyInactiveTabPanel={false}
        defaultActiveId={dimensionsLength > 1 ? 'line' : 'table'}
        addon={addon}
        activeId={active}
        onActive={(tab) => setActive(tab.id)}
      >
        {
            tabs.map((tab) => {
              const { id } = tab;
              if (id === 'sql') {
                return (
                  <TabPanel id={id} key={id}>
                    <Card.Body>
                      <ReactMarkdown
                        className={s.markdown}
                        components={{ code: CodeRender }}
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeRaw]}
                      >
                        {`\`\`\`sql
${sql ?? '暂无数据'}
\`\`\``}
                      </ReactMarkdown>
                    </Card.Body>
                  </TabPanel>);
              }
              if (id === 'table') {
                return (
                  <TabPanel id={id} key={id}>
                    <Card.Body>
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        className={s.markdown}
                        rehypePlugins={[rehypeRaw]}
                        components={{ code: CodeRender }}
                      >
                        {convertToMarkdownTable(realChartData)}
                      </ReactMarkdown>
                    </Card.Body>
                  </TabPanel>
                );
              }

              return (
                <TabPanel id={id} key={id}>
                  <Card.Body>
                    {
                      changeLoading && <div className={s.mask}>
                        <Icon
                          type="loading"
                        />
                      </div>
                    }
                    <ErrorBoundary>
                      <ReactEcharts
                        option={chartOption ?? {}}
                        style={{ height: height ? height - 70 : '300px', width: width ? width - 40 : '100%' }}
                      />
                    </ErrorBoundary>
                  </Card.Body>
                </TabPanel>
              );
            })
          }
      </Tabs>
    </Card>) : addon ? null : <Icon type="loading" style={{ marginLeft: 10, marginTop: 10 }} />;
};

export default ChartRender;
