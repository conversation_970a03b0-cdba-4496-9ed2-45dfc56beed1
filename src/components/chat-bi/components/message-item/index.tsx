/* eslint-disable jsx-a11y/aria-role */
/* eslint-disable max-len */
import { ContentType, MessageType, MessageTypeEnum } from '@/types';
import React, {
  useEffect, useMemo, useRef, useState,
} from 'react';
import { sleep } from '@src/utils';
import {
  Icon, Input,
} from '@tencent/tea-component';
import { ChatItem } from '@tencent/cloud-chat-ui';
import {
  CHANGE_INPUT_TIP,
  CHART_TYPE_SIGN,
  INPUT_MAX_LENGTH,
  NO_DATA_TIP,
  SQL_SIGN,
  TITLE_SIGN,
  MessageEventType,
} from '@src/contants';
import { v4 as uuidv4 } from 'uuid';
import { getEchartData, redrawChart } from '@src/utils/get-data';
import { useCommonSelector } from '@src/store/app-common';
import s from './index.module.scss';
import {
  extractSqlTags,
  getOptions,
  getUrlParam,
  scrollIntoView,
} from '../../utils';
import CollapseRender from '../collapse-render';
import ChartRender from '../chart-render';
import OperationsRender from '../operations-render';
import ChatReasoningRender from '../chat-reasoning-render';
import EmailTplSettingCard from '../email-tpl-setting-card';

interface MessageItemProps {
  item: MessageType;
  handleSendMessage: (message: string, options?: {
    addLast?: boolean;
    error?: string;
  }) => void;
  loading?: boolean;
  isLatest?: boolean;
  isFirst?: boolean;
  sessionId?: string;
  question?: string;
  apis: AppPluginAPI.PluginAPI;
  model: string;
  isInsight: boolean;
}

// 单个消息组件
const MessageItem = (props: MessageItemProps) => {
  const {
    item, handleSendMessage, loading, isLatest, isFirst, sessionId, question, apis, model, isInsight,
  } = props;
  const { emailTplSetIdList } = useCommonSelector();
  const isUser = item.type === MessageTypeEnum.user;
  // const isIframe = item.contentType === ContentType.iframeUrl;
  // const isChart = item.contentType === ContentType.chart;
  const sqlRef = useRef('');
  const titleRef = useRef('');
  const chartTypeRef = useRef('line');
  const retryRef = useRef(0);
  const { questions } = item;

  const [showDetail, setShowDetail] = useState(true); // 是否折叠，展示回答
  const [chartData, setChartData] = useState(undefined); // 用于展示的echart数据
  const [sqlLoading, setSqlLoading] = useState(false); // 是否展示loading
  const [finish, setFinish] = useState(false); // 本轮回话是否结束
  const [sql, setSql] = useState(''); // 当前展示的sql
  const [currQuestion] = useState(question); // 当前展示的问题
  const [showChangeInput, setShowChangeInput] = useState(false); // 是否展示修改输入框
  const [changeInputValue, setChangeInputValue] = useState(''); // 修改输入框的值
  const [options, setOptions] = useState(undefined); // echarts的options
  const [changeLoading, setChangeLoading] = useState(false); // 修改图表loading
  const [dataLimitTip, setDataLimitTip] = useState(''); // 数据量过大提示
  const isConsole = apis?.env === 'CONSOLE';

  useEffect(() => {
    if (!isUser && !sqlLoading && questions?.length > 0) {
      sleep(50).then(() => {
        scrollIntoView();
      });
    }
    if (!isUser && !sqlLoading) {
      sleep(50).then(() => {
        scrollIntoView();
      });
    }
  }, [sqlLoading, questions]);

  // 推理完成后获取图表数据
  useEffect(() => {
    if (!loading && isLatest && !isFirst) {
      const currSql = sqlRef.current;
      setSql(currSql);
      sqlRef.current = '';
      if (currSql) {
        setSqlLoading(true);
        getEchartData({
          isConsole,
          apiParams: {
            Action: 'GetChartDataSet',
            DataSQL: btoa(unescape(encodeURIComponent(currSql))),
            SessionId: String(sessionId),
            AppId: apis?.appId ?? Number(getUrlParam('appid')),
            ArchId: apis?.archInfo?.archId,
            NodeUUID: item?.currentNodeKey,
            ChatId: item.requestId,
          },
        }).then((d) => {
          // 处理异常
          if ((d as any)?.Error || !d) {
            setFinish(false);
            retryRef.current += 1;
            if (retryRef.current > 2) {
              setChartData({});
              setShowDetail(true);
              setFinish(true);
              return;
            }
            setChartData({});
            setShowDetail(true);
            handleSendMessage(question, {
              addLast: true,
              error: (d as any)?.Error?.Message,
            });
          } else {
            try {
              if (d) {
                const data = JSON.parse(d?.DataSet);
                setChartData(data);
                const options = getOptions(chartTypeRef.current, data, titleRef.current);
                setOptions(options);
                setShowDetail(false);
                if (d?.Warning) {
                  setDataLimitTip(d?.Warning);
                }
              }
            } catch (e) {
              console.error(e);
              setChartData({});
              setShowDetail(true);
            }
            setFinish(true);
          }
        }).catch((e) => {
          console.error(e);
          setChartData({});
        }).finally(() => {
          setSqlLoading(false);
        });
      } else {
        setFinish(true);
        setShowDetail(true);
        sqlRef.current = '';
      }
    }
  }, [loading, isLatest]);

  // 再问一次当前问题
  const handleRefresh = () => {
    handleSendMessage(currQuestion);
  };

  const currNodeInfo = useMemo(() => {
    let currNode = {
      key: '',
      name: '',
      svg: '',
      type: '',
    };
    apis?.archInfo?.nodeList?.forEach((nodeItem) => {
      if (item?.currentNodeKey === nodeItem?.DiagramId) {
        currNode = {
          key: nodeItem.DiagramId,
          name: nodeItem.NodeName,
          svg: '',
          type: nodeItem.ProductType,
        };
        try {
          const node = document.getElementById(nodeItem.DiagramId);
          const svgElement = node.querySelector('svg');
          const serializer = new XMLSerializer();
          const svgString = serializer.serializeToString(svgElement);
          currNode.svg = svgString;
        } catch (e) {
          console.error(e);
        }
      }
    });
    return currNode;
  }, [item?.currentNodeKey]);

  const handleChangeCurrChart = () => {
    setShowChangeInput(true);
  };

  // 修改图表
  const handleSendChangeMessage = () => {
    setChangeInputValue('');
    setChangeLoading(true);
    const optionsData = {
      dimensions: chartData?.dimensions,
      source: chartData?.source?.slice(0, 10),
    };
    const optionParam = {
      ...options,
      dataset: optionsData,
    };
    const queryParams = {
      Question: changeInputValue,
      SessionID: uuidv4(),
      Uin: apis?.uin,
      Action: 'RedrawChart',
      ApiSource: model,
      ChartOptions: JSON.stringify(optionParam),
      ArchId: apis?.archInfo?.archId,
      NodeUUID: item?.currentNodeKey,
      AppId: apis?.appId ?? Number(getUrlParam('appid')),
    };

    redrawChart({ apiParams: queryParams, isConsole }).then((d) => {
      try {
        const newOptions = {
          ...JSON.parse(d?.Answer),
          dataset: chartData,
        };
        setOptions(newOptions);
      } catch (e) {
        console.error(e.message);
      }
    }).finally(() => {
      setChangeLoading(false);
    });
  };

  const showChart = ((!isUser && !isFirst && !isLatest)
  || (!loading && isLatest && !isFirst))
  && chartData && Object.keys(chartData).length > 0
  && !isInsight;

  // 预处理文本，主要做标签内容提取与文本替换
  const handleContentText = (content: string) => {
    const allContent = item?.contentList?.join('');
    // 获取sql
    const { extracted } = extractSqlTags(allContent, SQL_SIGN);
    if (extracted.length > 0 && !isInsight) {
      sqlRef.current = extracted[extracted.length - 1];
    }

    // 获取标题后删除标签内容
    const { extracted: title, remaining } = extractSqlTags(content, TITLE_SIGN);
    titleRef.current = currNodeInfo.name ? `${currNodeInfo.name}：${title[0]}` : title[0];

    // 获取chartType后删除标签内容
    const { extracted: chartType, remaining: cr } = extractSqlTags(remaining, CHART_TYPE_SIGN);
    chartTypeRef.current = chartType[0] ?? 'line';

    const reasoningContent = item?.reasoningContent;
    const show = cr?.replaceAll(SQL_SIGN, `
\`\`\`
`);
    return {
      show,
      reasoningContent,
    };
  };

  const renderContent = (item: MessageType) => {
    switch (item.contentType) {
      case ContentType.text: {
        if (isUser) {
          return item.contentList[0];
        }
        const allContent = item?.contentList?.join('');
        const { show } = handleContentText(allContent || '正在分析，请稍候...');
        return (
          <>
            <CollapseRender
              apis={apis}
              str={show}
              isFirst={isFirst}
              isUser={isUser}
              finish={finish}
              loading={loading}
              isLatest={isLatest}
              showDetail={showDetail}
              setShowDetail={setShowDetail}
              item={item}
            />
            {
              showChart && <ChartRender
                chartData={chartData}
                sql={sql}
                changeLoading={changeLoading}
                options={options}
              />
            }
            {
              sqlLoading && <Icon type="loading" style={{ marginLeft: 10, marginTop: 10 }} />
            }
            {
              chartData && !Object.keys(chartData).length && finish && <div className={s['no-data']}>{NO_DATA_TIP}</div>
            }
            {
              dataLimitTip && <div className={s['no-data']}>{dataLimitTip}</div>
            }
            {
              ((!isFirst && finish) || !isLatest) && (
                <OperationsRender
                  currNodeInfo={currNodeInfo}
                  apis={apis}
                  showChart={showChart}
                  handleChangeCurrChart={handleChangeCurrChart}
                  sql={sql}
                  options={options}
                  handleRefresh={handleRefresh}
                  chatId={item.id}
                  requestId={item.requestId}
                  isLatest={isLatest}
                  saveTheme={() => {
                    handleSendMessage('保存治理主题');
                  }}
                  topicId={item.extra?.topic_id}
                />
              )
            }
            {
              showChangeInput && showChart && (
                <div className={s['change-input']}>
                  <Input
                    maxLength={INPUT_MAX_LENGTH}
                    placeholder={CHANGE_INPUT_TIP}
                    className={s.input}
                    autoFocus
                    value={changeInputValue}
                    onChange={setChangeInputValue}
                    onPressEnter={handleSendChangeMessage}
                    disabled={changeLoading}
                  />
                  <Icon type="close" className={s.icon} onClick={() => setShowChangeInput(false)} />
                </div>
              )
            }
          </>);
      }
      default:
        return null;
    }
  };
  return (
    <>
      {
        (item?.reasoningContent || item?.event === MessageEventType.governTopicThinking) && (
          <ChatReasoningRender
            reasoningContent={item?.reasoningContent}
            eventStatus={item?.eventStatus}
            isStop={item?.thinkingStop}
          />
        )
      }
      {
        (!item.event || item.event === MessageEventType.governTopicMessage) && (
          <ChatItem
            key={item?.id}
            role={item?.type === 'user' ? 'user' : 'assistant'}
            status={item.status}
            loadingText={item.loadingText}
            errorText={item.errorText}
          >
            {renderContent(item)}
            {
              (finish || isFirst) && questions?.length > 0 && (
                <div className={s['recommend-title']}>
                  推荐问题
                </div>
              )
            }
            {
              (finish || isFirst) && (
                <div className={s.questions}>
                  {questions?.map((q) => <div onClick={() => handleSendMessage(q)} className={s.question} key={q}>{q}</div>)}
                </div>
              )
            }
          </ChatItem>
        )
      }
      {
        (!isFirst && finish)
          && item.extra?.topic_id
          && emailTplSetIdList.includes(item.id)
          && (
            // eslint-disable-next-line jsx-a11y/aria-role
            <ChatItem role="assistant">
              <EmailTplSettingCard
                apis={apis}
                topicId={item.extra?.topic_id}
              />
            </ChatItem>
          )
      }
    </>
  );
};

export default MessageItem;
