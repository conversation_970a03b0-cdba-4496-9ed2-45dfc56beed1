.item {
  position: relative;
  width: 100%;

  .no-data {
    margin-top: 5px;
    color: #ff7200;
    font-weight: 500;
  }

  .recommend-title {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 600;
  }


  .content {
    width: fit-content;
    max-width: 90%;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 4Px 24Px 0 rgba(41,91,156,.05);
  }

  .questions {
    margin-top: 10px;
    font-weight: 600;

    .question {
      width: fit-content;
      color: #006ef9;
      cursor: pointer;
      line-height: 20px;
    }
  }

  .change-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 6px;
    gap: 10px;

    .input {
      flex: 1;
      border-radius: 10px;
    }

    .icon {
      flex-basis: 15px;
      cursor: pointer;
    }
  }
}

.bot {
  background-color: #fef8f8;

  &::after {
    position: absolute;
    top: 15px;
    left: -14px;
    width: 0;
    height: 0;
    border: 5px solid rgb(151, 242, 160);
    border-radius: 50%;
    content: '';
  }
}

.user {
  background-color: #006ef9;
  color: #fff;
  float: right;
  white-space: pre-wrap;
}

:global {
  /* 使用 fixed 定位动画中移动的元素 */
  .animated-button {
    position: fixed;
    z-index: 1000;
    z-index: 10000;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #e74c3c;
    pointer-events: none;
  }
}