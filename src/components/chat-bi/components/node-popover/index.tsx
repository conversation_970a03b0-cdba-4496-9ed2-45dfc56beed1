import { Bubble } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';

interface NodePopoverProps {
  content: any;
  onDestroy: () => void;
}
const NodePopover = (props: NodePopoverProps) => {
  const { content, onDestroy } = props;
  const [visible, setVisible] = useState(true);
  useEffect(() => {
    setTimeout(() => {
      setVisible(false);
      onDestroy?.();
    }, 1000);
  }, []);
  return <Bubble visible={visible} tooltip content={content}><div /></Bubble>;
};

export default NodePopover;
