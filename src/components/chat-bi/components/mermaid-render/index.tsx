/* eslint-disable import/no-extraneous-dependencies */
import React, { useEffect, useRef } from 'react';
import mermaid from 'mermaid';
import { scrollIntoView } from '../../utils';

const MermaidRenderer = ({ code }: any) => {
  const mermaidRef = useRef();

  useEffect(() => {
    try {
      mermaid.initialize({ startOnLoad: true });
      mermaid.init(undefined, mermaidRef.current);
      scrollIntoView();
    } catch (e) {
      console.log(e);
    }
  }, []);

  return <div className="mermaid" ref={mermaidRef}>{code}</div>;
};

const MermaidRender = ({ code }: any) => <MermaidRenderer code={code} />;

export default MermaidRender;
