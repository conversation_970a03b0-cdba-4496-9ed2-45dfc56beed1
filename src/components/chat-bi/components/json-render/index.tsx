import React, { memo, useMemo } from 'react';
import ReactEcharts from 'echarts-for-react';
import { parse } from 'best-effort-json-parser';
import { ChatCollapse } from '@tencent/cloud-chat-ui';
import { getOptions } from '../../utils';

const JsonRender = ({ finalSql }: any) => {
  if (!finalSql.trim()) {
    return null;
  }

  const parseData = useMemo(() => {
    try {
      const data = parse(finalSql);
      if (data?.agent && !data?.is_finish) {
        delete data?.agent;
        data.is_finish = false;
      }
      if (data?.agent && data?.is_finish) {
        delete data?.agent;
      }
      return data;
    } catch (error) {
      return null;
    }
  }, [finalSql]);

  const echarts = useMemo(() => {
    if (!parseData) {
      return null;
    }

    if (parseData?.thought || parseData?.DataSet) {
      const { DataSet: data } = parseData;

      if (data) {
        try {
          const parseData = JSON.parse(data);
          const options = getOptions('line', parseData, '');
          return <ReactEcharts
            option={options ?? {}}
            style={{ height: '300px', width: '100%' }}
            key={finalSql}
          />;
        } catch (e) {
          return null;
        }
      }

      return (
        <ChatCollapse defaultActiveKeys={['1']}>
          <ChatCollapse.Panel header={`${parseData?.id} ${parseData?.title}`} panelKey="1">
            {parseData.thought}
          </ChatCollapse.Panel>
        </ChatCollapse>
      );
    }

    return null;
  }, [parseData]);

  return (
    <>
      {echarts}
    </>
  );
};

export default memo(JsonRender, (pre, next) => pre.finalSql === next.finalSql);
