/**
 * @description 每个消息下方的操作项
 */
import React, { useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import {
  Bubble,
  Button, Checkbox, Icon, Input, message, Modal, Tag,
} from '@tencent/tea-component';
import { changeCommonData, useCommonSelector } from '@src/store/app-common';
import {
  DashboardItemTypeEnum, INPUT_MAX_LENGTH, MAX_CHART_COUNT, MAX_CHART_TIP,
} from '@src/contants';
import { addGraphToDashboardFinal, createFeedBack } from '@src/utils/get-data';
import {
  ChevronRightIcon,
} from '@tencent/tea-icons-react';
import s from './index.module.scss';
import { getUrlParam, animateButton, getDrawerWidth } from '../../utils';

const { TextArea } = Input;

interface OperationsRenderProps {
  currNodeInfo: {
    key: string;
    name: string;
    svg: string;
    type: string;
  };
  apis: AppPluginAPI.PluginAPI;
  showChart: boolean;
  handleChangeCurrChart: () => void;
  sql: string;
  options: any;
  handleRefresh: () => void;
  saveTheme: () => void;
  chatId: string;
  requestId: string;
  isLatest: boolean;
  topicId?: string;
}
const OperationsRender = (props: OperationsRenderProps) => {
  const {
    currNodeInfo,
    apis,
    showChart,
    handleChangeCurrChart,
    sql,
    options,
    handleRefresh,
    chatId,
    isLatest,
    saveTheme,
    topicId,
  } = props;
  const dispatch = useDispatch();
  const { dashBoardId, sessionId, dashboardItemCount } = useCommonSelector();
  const [status, setStatus] = useState(0); // 0 未点赞，1 已点赞，-1 已踩
  const [tempStatus, setTempStatus] = useState(0); // 临时状态0 未点赞，1 已点赞，-1 已踩
  const [visible, setVisible] = useState(false);
  const [feedback, setFeedback] = useState(''); // 反馈内容
  const [addLoading, setAddLoading] = useState(false); // 添加到看板loading
  const [check, setCheck] = useState(false); // 是否接受回访

  const addDashRef = useRef(null);
  const isConsole = apis?.env === 'CONSOLE';

  // 点击节点，放大到中间
  const handleZoomNode = (key: string) => {
    if (key) {
      const drawerWidth = getDrawerWidth();
      apis?.zoomNodeToPosition?.(key, { rightWidth: drawerWidth });
    }
    dispatch(changeCommonData({
      selectedNode: key,
    }));
  };

  const handleAddDashBoard = () => {
    if (addLoading) return;
    if (dashboardItemCount >= MAX_CHART_COUNT) {
      message.warning({ content: MAX_CHART_TIP });
      return;
    }
    setAddLoading(true);
    const AppId = apis?.appId ?? Number(getUrlParam('appid'));
    const ArchId = apis?.archInfo?.archId;
    const Uin = apis.uin;
    const item = {
      AppId,
      ArchId,
      Uin,
      DashBoardId: dashBoardId,
      DataSetSQL: sql,
      ChartOptions: JSON.stringify(options),
      GraphDesc: '',
      GraphName: options?.title?.text ?? '',
      Position: [0, 0, 12, 10],
      GraphType: DashboardItemTypeEnum.sql,
      DiagramId: currNodeInfo?.key,
      NodeName: currNodeInfo?.name,
      NodeType: currNodeInfo?.type,
    };
    addGraphToDashboardFinal({ apiParams: item, isConsole }).then(() => {
      dispatch(changeCommonData({
        dashboardItemCount: dashboardItemCount + 1,
      }));
      animateButton(addDashRef.current, document.getElementById('dashboard-tab'));
    }).finally(() => {
      setAddLoading(false);
    });
  };

  // 点赞或点踩
  const changeMessageStatus = (status) => {
    setTempStatus(status);
    setVisible(true);
  };

  const onClose = () => {
    setVisible(false);
  };

  // 处理提交点赞点踩
  const handleCommitFeedback = (status?: number) => {
    const currStatus = status ?? tempStatus;
    createFeedBack({
      isConsole,
      apiParams: {
        AppId: apis?.appId ?? Number(getUrlParam('appid')),
        Action: 'CreateMessageFeedBack',
        ArchId: apis?.archInfo?.archId,
        ChatId: chatId,
        FeedBack: currStatus,
        SessionId: sessionId,
        Uin: apis?.uin,
        FeedBackMsg: feedback,
        IsAcceptReturnVisit: check,
      },
    }).then((d) => {
      if (d) {
        setStatus(currStatus);
      }
    }).finally(() => {
      setVisible(false);
      setFeedback('');
      setCheck(false);
    });
  };

  return (
    <div className={s.operations}>
      {
        topicId && (
          <span
            className={s['view-theme-button']}
            onClick={() => {
              const event = new CustomEvent('switchGovernanceTab');
              window.dispatchEvent(event);
            }}
          >
            <span className={s['button-label']}>操作</span>
            <span className={s['button-text']}>查看主题</span>
            <ChevronRightIcon size={16} />
          </span>
        )
      }
      {
        !topicId && (
          <>
            <span
              onClick={() => handleZoomNode(currNodeInfo.key)}
              className={s.selected}
            >
              查询范围：
              {
                currNodeInfo.key ? (
                  <Tag theme="primary">
                    <span
                      // eslint-disable-next-line react/no-danger
                      dangerouslySetInnerHTML={{ __html: currNodeInfo.svg }}
                      className={s.svg}
                    />
                    <span className={s.text}>
                      {currNodeInfo.name}
                    </span>
                  </Tag>) : (
                    <Tag
                      theme="primary"
                    >
                      整个架构图
                    </Tag>
                )
              }
            </span>
            {
              showChart && (
                <div className={s.tool} onClick={handleChangeCurrChart}>
                  修改样式
                </div>
              )
            }
            {
              showChart && (
                <div
                  className={s.tool}
                  onClick={handleAddDashBoard}
                  ref={addDashRef}
                >
                  添加到dashboard
                  {
                  addLoading && <div className={s.loading}><Icon type="loading" /></div>
                }
                </div>
              )
            }
            {
              showChart && isLatest && (
                <div
                  className={s.tool}
                  onClick={() => {
                    dispatch(changeCommonData({
                      isInsightMode: true,
                    }));
                  }}
                >
                  查根因
                </div>
              )
            }
            <div
              style={{ flex: 1 }}
              className={s['right-tools']}
            >
              <div
                className={s['save-theme']}
                style={{ marginRight: 5 }}
                onClick={() => {
                  saveTheme();
                }}
              >
                保存治理主题
              </div>
              <div
                className={s.tool}
                onClick={() => changeMessageStatus(status === -1 ? 0 : -1)}
              >
                反馈bad case
              </div>
              <Bubble
                content="重新生成"
                tooltip
              >
                <Icon
                  type="refresh"
                  className={s['right-icon']}
                  onClick={handleRefresh}
                />
              </Bubble>
            </div>
          </>
        )
      }
      <Modal
        visible={visible}
        caption={tempStatus === 1 ? '谢谢你的认可，我们会继续优化进步' : '谢谢你的反馈，我们会继续优化进步'}
        onClose={onClose}
        maskClosable
      >
        <Modal.Body>
          <TextArea
            maxLength={INPUT_MAX_LENGTH}
            placeholder="请写下更多反馈内容反馈内容"
            style={{ width: '100%' }}
            value={feedback}
            onChange={setFeedback}
          />
          <br />
          <Checkbox value={check} onChange={setCheck} style={{ marginTop: 10 }}>是否愿意接受研发团队的回访</Checkbox>
        </Modal.Body>
        <Modal.Footer>
          <Button type="primary" onClick={() => handleCommitFeedback()}>
            提交
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default OperationsRender;
