.operations {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 10px;
  font-weight: 500;
  gap: 10px;

  .right-tools {
    display: flex;
    flex: 1;
    justify-content: flex-end;

    .right-icon {
      position: relative;
      top: 2px;
      width: 16px;
      height: 16px;
      flex-shrink: 0;
      margin-right: 5px;
      margin-left: 5px;
      cursor: pointer;

      &:hover {
        opacity: .6;
      }
    }

    .save-theme {
      position: relative;
      display: flex;
      overflow: hidden;
      min-width: 100px;
      box-sizing: border-box;
      align-items: center;
      justify-content: center;
      padding: 2px 10px;
      border: 1px solid #e5e5e5;
      border-radius: 5px;
      background-color: #006eff;
      box-shadow: 0 6Px 6Px 0 rgba(0,0,0,.04),0 8Px 24Px 0 rgba(0,0,0,.02);
      color: #fff;
      cursor: pointer;
      font-weight: 600;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .tool {
    position: relative;
    display: flex;
    overflow: hidden;
    min-width: 100px;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    padding: 2px 10px;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 6Px 6Px 0 rgba(0,0,0,.04),0 8Px 24Px 0 rgba(0,0,0,.02);
    cursor: pointer;
    font-weight: 600;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      background-color: #eae7e7;
    }

    .loading {
      position: absolute;
      top: 0;
      display: flex;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
      background-color: #eae7e7;
      opacity: .5;
    }
  }

  .disabled {
    position: relative;
    background-color: #f7e4e4;
    cursor: not-allowed;

    &:hover {
      background-color: #f7e4e4;
    }

    &:after {
      position: absolute;
      position: absolute;
      top: -12px;
      right: -6px;
      max-width: none;
      height: 15px;
      padding: 1px 6px;
      border-radius: 8px;
      background: rgb(255, 232, 214);
      color: rgb(254, 113, 32);
      content: 'alpha';
      font-family: 'PingFang SC';
      font-size: 8px;
      font-style: normal;
      font-weight: 400;
      line-height: 12px;
      text-align: center;
    }
  }

  :global {
    .sdk-ai-bi-for-inspection-tag {
      margin: 0;
    }
  }

  .selected {
    cursor: pointer;

    .text {
      position: relative;
      top: 1px;
    }

    svg {
      width: 16px;
      height: 16px;
      margin: 0 5px;
      cursor: pointer;

      .shape-body {
        .fill-dark {
          fill: yellow;
        }
      }
    }
  }

  .view-theme-button {
    display: flex;
    height: 28px;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
    border: 1px solid #e9ecf1;
    border-radius: 18px;
    cursor: pointer;

    .button-label {
      display: flex;
      display: block;
      height: 16px;
      box-sizing: border-box;
      align-items: center;
      justify-content: center;
      padding: 0px 6px;
      border-radius: 8px;
      background: #f2f4f8;
      color: rgba(0, 0, 0, .50);
      font-size: 10px;
    }

    .button-text {
      margin: 0 5px;
      color: rgba(0, 0, 0, .70);
      font-size: 12px;
      font-weight: 400;
    }
  }
}