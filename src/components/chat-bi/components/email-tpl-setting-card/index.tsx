/**
 * @description 邮件模版配置卡片
 */
import React, { useEffect, useState } from 'react';
import {
  Form,
  Button,
  TimePicker,
  Select,
  Input,
  message,
  SelectMultiple,
} from '@tencent/tea-component';
import { app } from '@tencent/tea-app';
import {
  describeRiskManageSubjectListFinal,
  describeSubAccountsByMainAccountFinal,
  describeSubscriptionEmailListV2Final,
  createSubscriptionEmailV2Final,
  updateSubscriptionEmailV2Final,
  updateSubscriptionV2Final,
} from '@src/utils/get-data';
import { getPopupContainer } from '@src/utils/common-funcs';
import { useForm, Controller } from 'react-hook-form';
import { getUrlParam } from '@src/components/chat-bi/utils';
import { FREQUENCY_OPTS, WEEK_OPTS, MONTH_OPTS } from '@src/contants/risk-subject';
import s from './index.module.scss';

interface IProps {
  apis: AppPluginAPI.PluginAPI;
  topicId: string;
}

const EmailTplSettingCard = (props: IProps) => {
  const {
    apis,
    topicId,
  } = props;
  const {
    control,
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const watchPeriod = watch('Period');
  const watchAccount = watch('Account');
  const watchEmail = watch('Email');
  const isConsole = apis?.env === 'CONSOLE';
  const [loading, setLoading] = useState(false);
  const [accountList, setAccountList] = useState([]);
  const [subscriptionListOption, setSubscriptionListOption] = useState([]);
  const [emailInputDisabled, setEmailInputDisabled] = useState(false);
  const [emailInfoList, setEmailInfoList] = useState([]);
  const [matchEmailInfo, setMatchEmailInfo] = useState<any>({});
  const [accountUin, setAccountUin] = useState('');
  const [startBtnDisabled, setStartBtnDisabled] = useState(false);
  const getBaseParams = () => ({
    AppId: apis?.appId ?? Number(getUrlParam('appid')),
    ArchId: apis?.archInfo?.archId,
    Uin: apis.uin,
    SubAccountUin: apis.uin,
  });
  const getStatus = (fieldState) => {
    if (fieldState?.error?.message) {
      return 'error';
    }
    if (!fieldState.isDirty) {
      return undefined;
    }
    return fieldState.invalid ? 'error' : 'success';
  };

  const onSubmitHandle = async (values) => {
    setLoading(true);
    try {
      const currentTopic = subscriptionListOption?.find((item) => item.value === values.SubscriptionTopic) || {};
      const res: any = await updateSubscriptionV2Final({
        isConsole,
        apiParams: {
          ...getBaseParams(),
          SubscriptionInfos: [
            {
              Id: -1,
              ArchIds: [apis?.archInfo?.archId],
              EmailInfo: {
                EmailAddrs: [values.Email],
              },
              EmailTitle: `腾讯云顾问（Advisor）${currentTopic?.text}-${apis?.archInfo?.archId}-${accountUin}`,
              IsHideCusName: false,
              IsSubscribed: true,
              ReportType: 1,
              SubjectId: values.SubscriptionTopic,
              Name: `${currentTopic?.text}-${apis?.archInfo?.archId}`,
              TimeInfo: {
                Period: values.Period,
                Time: values.Time.format('HH:00'),
                Monthly: values.Monthly ? values.Monthly.join(',') : '',
                DaysOfWeek: values.DaysOfWeek ? values.DaysOfWeek.join(',') : '',
              },
            },
          ],
        },
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      message.success({ content: '保存成功' });
      setStartBtnDisabled(true);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      const msg = err.msg || err.toString() || '未知错误';
      message.error({ content: msg });
    }
    if (values.Email) {
      const userInfoArr = values.Account?.split(':');
      if (matchEmailInfo.Id !== undefined) {
        await updateSubscriptionEmailV2Final({
          isConsole,
          apiParams: {
            ...getBaseParams(),
            Id: matchEmailInfo.Id,
            SubUin: userInfoArr[1],
            UserName: matchEmailInfo.UserName ? matchEmailInfo.UserName : userInfoArr[0],
            Email: values.Email,
          },
        });
      } else {
        await createSubscriptionEmailV2Final({
          isConsole,
          apiParams: {
            ...getBaseParams(),
            SubUin: userInfoArr[1],
            UserName: userInfoArr[0],
            Email: values.Email,
          },
        });
      }
    }
  };
  const getSubscriptionTopicList = async () => {
    try {
      const res: any = await describeRiskManageSubjectListFinal({
        isConsole,
        apiParams: {
          ...getBaseParams(),
          Limit: 10,
          Offset: 0,
        },
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      setSubscriptionListOption(res?.Items?.map((item) => ({ text: item.Title, value: item.Id })));
    } catch (err) {
      const msg = err.msg || err.toString() || '未知错误';
      message.error({ content: msg });
    }
  };

  const getSubAccountsByMainAccount = async () => {
    try {
      const res: any = await describeSubAccountsByMainAccountFinal({
        isConsole,
        apiParams: {
          ...getBaseParams(),
        },
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      setAccountList(res.SubAccountInfoList);
    } catch (err) {
      const msg = err.msg || err.toString() || '未知错误';
      message.error({ content: msg });
    }
  };

  const getDescribeSubscriptionEmailListV2 = async () => {
    try {
      const res: any = await describeSubscriptionEmailListV2Final({
        isConsole,
        apiParams: {
          ...getBaseParams(),
          Limit: -1,
          Offset: 0,
        },
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      setEmailInfoList(res.EmailDetails ?? []);
    } catch (err) {
      const msg = err.msg || err.toString() || '未知错误';
      message.error({ content: msg });
    }
  };

  useEffect(() => {
    if (isConsole) {
      app.user.current().then(({ loginUin }) => {
        setAccountUin(`${loginUin}`);
      });
    } else {
      setAccountUin(apis?.uin);
    }
    if (topicId) {
      setValue('SubscriptionTopic', +topicId);
    }
    getSubscriptionTopicList();
    getSubAccountsByMainAccount();
    getDescribeSubscriptionEmailListV2();
  }, []);

  useEffect(() => {
    if (watchAccount) {
      const subUin = watchAccount?.split(':')[1];
      const item = emailInfoList.find((item) => item?.SubUin === subUin);
      if (item) {
        setValue('Email', item.Email);
        setEmailInputDisabled(true);
      } else {
        setValue('Email', '');
        setEmailInputDisabled(false);
      }
    }
  }, [watchAccount, emailInfoList]);

  useEffect(() => {
    if (watchEmail) {
      const item = emailInfoList.find((item) => item?.Email === watchEmail);
      if (item) {
        setMatchEmailInfo(item);
      } else {
        setMatchEmailInfo({});
      }
    }
  }, [watchEmail, emailInfoList]);

  return (
    <div className={s['email-tpl-setting-card']}>
      <p>风险主题邮件订阅配置</p>
      <Form
        className={s['form-container-box']}
        layout="vertical"
      >
        <Controller
          name="SubscriptionTopic"
          control={control}
          rules={{
            validate: (value) => (!value ? '请选择主题' : undefined),
          }}
          render={({ field, fieldState }) => (
            <Form.Item
              required
              label="订阅主题"
              status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
              message={errors.SubscriptionTopic?.message}
            >
              <Select
                searchable
                style={{ width: '100%' }}
                {...field}
                appearance="button"
                matchButtonWidth
                placeholder="请选择主题"
                options={subscriptionListOption}
                disabled={startBtnDisabled}
                popupContainer={getPopupContainer()}
              />
            </Form.Item>
          )}
        />
        <div className={s['form-item-time']}>
          <Controller
            name="Time"
            control={control}
            rules={{
              validate: (value) => (!value ? '请选择发送时间' : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                required
                label="发送时间"
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.Time?.message}
                style={{ width: '61%' }}
              >
                <TimePicker
                  className={s['form-time-picker']}
                  style={{ width: '100%' }}
                  {...field}
                  format="HH:00"
                  disabled={startBtnDisabled}
                  minuteStep={60}
                  popupContainer={getPopupContainer()}
                />
              </Form.Item>
            )}
          />
          <Controller
            name="Period"
            control={control}
            rules={{
              validate: (value) => (!value ? '请选择发送频率' : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                required
                label="发送频率"
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.Period?.message}
                style={{ width: '50%', marginLeft: '10px' }}
              >
                <Select
                  size="full"
                  {...field}
                  appearance="button"
                  matchButtonWidth
                  disabled={startBtnDisabled}
                  placeholder="请选择发送频率"
                  options={FREQUENCY_OPTS}
                  popupContainer={getPopupContainer()}
                />
              </Form.Item>
            )}
          />
        </div>
        {
          watchPeriod === 'Weekly'
          && <Controller
            name="DaysOfWeek"
            control={control}
            rules={{
              validate: (value) => (!value ? '请选择发送星期' : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                required
                label="发送星期"
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.DaysOfWeek?.message}
              >
                <SelectMultiple
                  style={{ width: '100%' }}
                  {...field}
                  disabled={startBtnDisabled}
                  appearance="button"
                  matchButtonWidth
                  placeholder="请选择发送星期"
                  options={WEEK_OPTS}
                  popupContainer={getPopupContainer()}
                />
              </Form.Item>
            )}
          />
        }
        {
          watchPeriod === 'Monthly'
          && <Controller
            name="Monthly"
            control={control}
            rules={{
              validate: (value) => (!value ? '请选择发送日期' : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                required
                label="发送日期"
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.Monthly?.message}
              >
                <SelectMultiple
                  style={{ width: '100%' }}
                  {...field}
                  disabled={startBtnDisabled}
                  appearance="button"
                  matchButtonWidth
                  placeholder="请选择发送日期"
                  options={MONTH_OPTS}
                  popupContainer={getPopupContainer()}
                />
              </Form.Item>
            )}
          />
        }
        <Controller
          name="Account"
          control={control}
          rules={{
            validate: (value) => (!value ? '请选择接收账号' : undefined),
          }}
          render={({ field, fieldState }) => (
            <Form.Item
              required
              label="接收账号"
              status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
              message={errors.Account?.message}
            >
              <Select
                searchable
                style={{ width: '100%' }}
                {...field}
                disabled={startBtnDisabled}
                appearance="button"
                matchButtonWidth
                placeholder="请选择接收账号"
                popupContainer={getPopupContainer()}
                options={accountList.map((item) => ({
                  text: `${item.Name}(${item.Uin})`,
                  value: `${item.Name}:${item.Uin}`,
                }))}
              />
            </Form.Item>
          )}
        />
        <Controller
          name="Email"
          control={control}
          rules={{
            validate: (value) => ((!/^(?!.*\s)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value) && value?.trim()?.length > 0) ? '邮箱格式不正确' : undefined),
          }}
          render={({ field, fieldState }) => (
            <Form.Item
              required
              label="邮箱地址"
              status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
              message={errors.Email?.message}
            >
              <Input
                disabled={emailInputDisabled || startBtnDisabled}
                {...field}
                style={{ width: '100%' }}
                placeholder="子账号未有邮箱，请填写以便接收风险主题邮件"
              />
            </Form.Item>
          )}
        />
      </Form>
      <div className={s['email-tpl-setting-card-footer']}>
        <Button
          loading={loading}
          type="primary"
          className={s['email-tpl-setting-card-btn']}
          onClick={handleSubmit(onSubmitHandle) as any}
          disabled={startBtnDisabled}
        >
          启用
        </Button>
      </div>
    </div>
  );
};

export default EmailTplSettingCard;
