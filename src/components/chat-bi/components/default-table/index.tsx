import { Table } from '@tencent/tea-component';
import React, { useMemo, useState } from 'react';
import { useCommonSelector } from '@src/store/app-common';
import s from './index.module.scss';

const { expandable } = Table.addons;

interface DefaultTableProps {
  finish: boolean;
  apis: AppPluginAPI.PluginAPI;
}
const DefaultTable = (props: DefaultTableProps) => {
  const { finish } = props;
  const [expandedKeys, setExpandedKeys] = useState([]);
  const { productsSchemaList } = useCommonSelector();

  const allColumns = [
    {
      key: 'ProductName',
      header: '产品名称',
    },
    {
      key: 'Count',
      header: '指标数量',
    },
  ];

  const allData = useMemo(() => productsSchemaList?.map((item) => ({
    ProductName: item?.ProductName ?? '-',
    Count: item?.Columns?.length ?? 0,
  })), [productsSchemaList]);

  const columns = [
    {
      key: 'ColumnName',
      header: '指标字段',
    },
    {
      key: 'ColumnComment',
      header: '说明（单位）',
    },
  ];

  const getChildData = (key) => {
    const currData = productsSchemaList?.find((d) => d.ProductName === key);
    return ({
      title: currData?.TableDescribe ?? '-',
      data: currData?.Columns ?? [],
    });
  };

  if (!finish) return null;

  return (
    <div className={s.table}>
      <Table
        verticalTop
        records={allData}
        recordKey="ProductName"
        columns={allColumns}
        addons={[
          expandable({
            expandedKeys,
            rowExpand: true,
            onExpandedKeysChange: (keys, { event }) => {
              event.stopPropagation();
              setExpandedKeys(keys);
            },
            gapCell: 1,
            render(record) {
              const childData = getChildData(record?.ProductName ?? '');
              return <div style={{ marginBottom: 10 }}>
                <div className={s.title} style={{ marginTop: 0 }}>{childData?.title}</div>
                <Table
                  verticalTop
                  records={childData?.data}
                  recordKey="ProductName"
                  columns={columns}
                />
              </div>;
            },
          }),
        ]}
      />
    </div>
  );
};

export default DefaultTable;
