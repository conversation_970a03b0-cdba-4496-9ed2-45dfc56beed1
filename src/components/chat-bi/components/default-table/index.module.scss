.table {
  margin-top: 10px;

  table {
    overflow: auto;
    width: 100%;
    max-height: 400px;
    border-collapse: separate;
    border-spacing: 0;

    th,
    td {
      padding: 8px !important;
      border: 1px solid #bcb9b9;
      border-top: 0;
      border-right: 0; /* 移除右边框 */
      border-left: 0; /* 移除左边框 */
      text-align: left;
    }
  
    th {
      background-color: #f8f8f8;
      font-weight: bold;
    }
  
    thead {
      position: sticky;
      top: 0;
  
      th {
        border-top: 0;
      }
    }
  
    tbody {
      tr {
        table-layout: fixed;
      }
    }
  }

  .title {
    margin: 7px 0;
    font-size: 12px;
    font-weight: 500;
  }
}