import React, {
  useMemo, useEffect, useRef, useState,
} from 'react';
import {
  ChatReasoning, ChatMarkdown,
} from '@tencent/cloud-chat-ui';
import rehypeRaw from 'rehype-raw';
import {
  MessageEventStatus,
} from '@src/contants';

interface IChatReasoningRenderProps {
  eventStatus: string;
  reasoningContent: string;
  isStop: boolean;
}

export default function ChatReasoningRender(props: IChatReasoningRenderProps): React.ReactElement {
  const {
    eventStatus,
    reasoningContent,
    isStop,
  } = props;
  const [second, setSecond] = useState(1);
  const timer = useRef(null);
  const secondRef = useRef(1);

  const status = useMemo(() => {
    if ((eventStatus === MessageEventStatus.start || eventStatus === MessageEventStatus.inProgress) && !isStop) {
      return 'loading';
    }
    if (eventStatus === MessageEventStatus.end || isStop) {
      return 'finished';
    }
    return 'loading';
  }, [eventStatus, isStop]);

  const statusText = useMemo(() => {
    if ((eventStatus === MessageEventStatus.start || eventStatus === MessageEventStatus.inProgress) && !isStop) {
      return '深度思考中...';
    }
    if ((eventStatus === MessageEventStatus.end) && !isStop) {
      return '深度思考已完成';
    }
    if (isStop) {
      return '思考已终止';
    }
    return '深度思考中...';
  }, [eventStatus, isStop]);

  const changeSecondHandel = () => {
    setSecond(secondRef.current + 1);
  };

  useEffect(() => {
    secondRef.current = second;
  }, [second]);

  useEffect(() => {
    if (!timer.current) {
      timer.current = setInterval(changeSecondHandel, (1000));
    }

    if (eventStatus === MessageEventStatus.end || isStop) {
      clearInterval(timer.current);
      timer.current = null;
    }
  }, [eventStatus, isStop]);

  return (
    <ChatReasoning
      statusIcon={status}
      statusText={statusText}
      time={`${second}s`}
    >
      <ChatMarkdown
        // eslint-disable-next-line react/no-array-index-key
        content={reasoningContent.trim()}
        markdownProps={{
          rehypePlugins: [rehypeRaw],
        }}
      />
    </ChatReasoning>
  );
}
