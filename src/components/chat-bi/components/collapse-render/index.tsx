/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect, useMemo } from 'react';
import { BOTTOM_ITEM_ID, NEED_GET_TABLE_SIGN, SQL_SIGN } from '@src/contants';
import { MessageType } from '@src/types';
import { AITrigger, ChatMarkdown } from '@tencent/cloud-chat-ui';
import rehypeRaw from 'rehype-raw';
import CodeRender from '../coder-render';
import DefaultTable from '../default-table';
import JsonRender from '../json-render';

const { DotLoading } = AITrigger;

interface CollapseComponentProps {
  str: string;
  isFirst: boolean;
  isUser: boolean;
  finish: boolean;
  loading: boolean;
  isLatest: boolean;
  apis: AppPluginAPI.PluginAPI;
  showDetail: boolean;
  setShowDetail: (show: boolean) => void;
  type?: string;
  startAnswer?: boolean;
  item: MessageType;
}
const CollapseComponent = (props: CollapseComponentProps) => {
  const {
    isFirst, isUser, str, finish, apis, showDetail, setShowDetail, type, startAnswer, item, isLatest,
  } = props;

  if (!str) {
    return null;
  }

  const isThink = type === 'think';
  const needGetTable = str.includes(NEED_GET_TABLE_SIGN);

  useEffect(() => {
    const needCloseThink = isThink && startAnswer;

    if (needCloseThink) {
      setShowDetail(false);
    }
  }, [type, startAnswer]);

  useEffect(() => {
    if (needGetTable && finish) {
      const bottomItem = document.getElementById(BOTTOM_ITEM_ID);
      if (bottomItem) {
        bottomItem?.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [finish, needGetTable]);

  const markdown = useMemo(() => {
    const resList = [];
    item?.contentList?.forEach((l) => {
      // @ts-ignore
      const currStr = l.replaceAll(SQL_SIGN, '```')?.trim();
      if (currStr.includes('```')) {
        const newList = currStr.split('```');
        newList.forEach((str, ind) => {
          if (ind % 2 === 0 && str.trim()) {
            resList.push(str);
          } else if (ind % 2 !== 0 && str.trim()) {
            resList.push(`\`\`\`${str}
\`\`\``);
          }
        });
      } else if (currStr) {
        resList.push(currStr);
      }
    });
    const lastMessage = resList[(resList?.length || 1) - 1] ?? '';
    const temp = lastMessage?.includes('```json')
    && (lastMessage?.includes('agent')
    || !lastMessage?.includes('thought'));
    const needLoading = !finish && (!resList?.length || temp) && isLatest;

    return <>
      {resList?.map((str, ind) => {
        const currStr = str.trim();
        if (currStr.startsWith('```json')) {
          const json = `${currStr.replace('```json', '').replace('```', '')}`;
          // eslint-disable-next-line react/no-array-index-key
          return <JsonRender finalSql={json} key={ind} />;
        }
        if (currStr.startsWith('```')) {
        // @ts-ignore
          const str = currStr.replaceAll(SQL_SIGN, '').replaceAll('```', '');
          return <CodeRender {...props} finish={finish} key={currStr} children={str} />;
        }
        return (str.trim() ? (
          <>
            <ChatMarkdown
          // eslint-disable-next-line react/no-array-index-key
              key={ind}
              content={str.trim()}
              markdownProps={{
                rehypePlugins: [rehypeRaw],
              }}
            />
          </>
        ) : null);
      })}
      {needLoading && <DotLoading />}
    </>;
  }, [finish, item]);

  return (
    <>
      {
        (isUser || isFirst || showDetail) && markdown
      }

      {
        needGetTable && <DefaultTable finish={finish} apis={apis} />
      }
    </>
  );
};

export default CollapseComponent;
