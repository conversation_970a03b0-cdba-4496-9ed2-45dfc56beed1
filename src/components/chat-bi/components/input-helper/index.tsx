import React, { useMemo } from 'react';
import { useCommonSelector } from '@src/store/app-common';
import {
  Bubble,
  Dropdown, List,
} from '@tencent/tea-component';
import lineSvg from '@src/assets/svgs/line.svg';
import SelectedNodes from '../selected-node';
import s from './index.module.scss';

interface InputHelperProps {
  apis: AppPluginAPI.PluginAPI;
  handleHelperWords: (words: string) => void;
  focusInput?: () => void;
}
const InputHelper = (props: InputHelperProps) => {
  const { apis, handleHelperWords, focusInput } = props;

  const {
    selectedNode, productsSchemaList, isInsightMode,
  } = useCommonSelector();

  const selectedNodeType = useMemo(() => {
    // 通过nodeList找到节点类型
    const currNodeType = apis?.archInfo?.nodeList?.find((item) => item?.DiagramId === selectedNode)?.ProductType;
    return currNodeType;
  }, [selectedNode]);

  // const v0Options = useMemo(() => {
  //   const validProducts = apis?.archInfo?.nodeList?.filter((item) => chatProducts?.includes(item?.ProductType));
  //   return validProducts?.map((item) => ({
  //     value: item?.DiagramId,
  //     text: item?.NodeName,
  //   })) ?? [];
  // }, [chatProducts]);

  const v1Options = useMemo(() => {
    const options: any = [{
      value: '选指标:',
      text: '指标:',
      disabled: true,
    }];
    const list = selectedNodeType
      ? productsSchemaList?.filter((item) => item.SigmaId === selectedNodeType)
      : productsSchemaList;
    list?.forEach((item) => {
      const { Columns: columns } = item;
      const schemaList = columns?.map((column) => ({
        value: `查询指标${column.ColumnComment}`,
        text: column.ColumnComment,
      })) ?? [];
      options.push(...schemaList);
    });

    return options;
  }, [productsSchemaList, selectedNodeType]);

  const v2Options = [{
    value: '选函数:',
    text: '函数:',
    disabled: true,
  }, {
    value: '采用函数均值avg()',
    text: '均值avg( )',
  }, {
    value: '采用函数最大值max()',
    text: '最大值max( )',
  },
  {
    value: '采用函数最小值min()',
    text: '最小值min( )',
  },
  {
    value: '采用函数累计sum()',
    text: '累计sum( )',
  }];

  const v3Options = [{
    value: '选时间范围:',
    text: '时间范围:',
    disabled: true,
  }, {
    value: '时间范围是过去24小时',
    text: '过去24小时',
  }, {
    value: '时间范围是过去三天',
    text: '过去三天',
  },
  {
    value: '时间范围是过去七天',
    text: '过去七天',
  }, {
    value: '时间范围是过去三十天',
    text: '过去三十天',
  }, {
    value: '时间范围是',
    text: '__自定义__',
  }];

  const v4Options = [{
    value: '汇总:',
    text: '汇总:',
    disabled: true,
  }, {
    value: '按实例汇总',
    text: '按实例',
  }, {
    value: '按节点汇总',
    text: '按节点',
    disabled: true,
  },
  {
    value: '',
    text: '按应用',
    disabled: true,
  }];

  const v5Options = [{
    value: '汇总:',
    text: '汇总:',
    disabled: true,
  }, {
    value: '按分钟汇总',
    text: '按分钟',
  }, {
    value: '按小时汇总',
    text: '按小时',
  },
  {
    value: '按天汇总',
    text: '按天',
  }];

  const v6Options = [{
    value: '选图表类型:',
    text: '图表类型:',
    disabled: true,
  }, {
    value: '用折线图展示',
    text: '折线图',
  },
  {
    value: '用柱状图展示',
    text: '柱状图',
  }, {
    value: '用饼图展示',
    text: '饼图',
  }];

  const handleChange = (v: string) => {
    handleHelperWords(v);
    focusInput();
  };

  // const handleChangeSelectNode = (node: string) => {
  //   dispatch(changeCommonData({
  //     selectedNode: node,
  //   }));
  // };

  const dropdownList = [{
    button: '选指标',
    options: v1Options,
  }, {
    button: '选计算函数',
    options: v2Options,
  },
  {
    button: '选时间段',
    options: v3Options,
  },
  {
    button: '选资源粒度',
    options: v4Options,
  },
  {
    button: '选时间粒度',
    options: v5Options,
  },
  {
    button: '选图表类型',
    options: v6Options,
  }];

  return <div className={s.helper} id="chatBi-helper">
    {/* <Dropdown
      trigger="hover"
      button={<SelectedNodes selectedNode={selectedNode} apis={apis} />}
      appearance="pure"
      placement="top-start"
      className={s.selected}
    >
      <List type="option">
        {v0Options?.map((item) => <List.Item
          key={item.text}
          onClick={() => handleChangeSelectNode(item.value)}

        >
          {item.text}
        </List.Item>)}
      </List>
    </Dropdown> */}
    <Bubble
      content="针对架构图提问或点击架构图里面的节点针对提问"
      tooltip
      popupContainer={() => document.getElementById('chatBi-helper')}
    >
      <div>
        <SelectedNodes selectedNode={selectedNode} apis={apis} />
      </div>
    </Bubble>

    {
      !isInsightMode && dropdownList?.map((item) => (
        <>
          <img src={lineSvg} alt="lineSvg" />
          <Dropdown
            trigger="hover"
            button={item.button}
            appearance="pure"
            placement="top-start"
          >
            <List type="option">
              {item.options?.map((item) => <List.Item
                key={item.text}
                onClick={() => handleChange(item.value)}
                disabled={item.disabled}
              >
                {item.text}
              </List.Item>)}
            </List>
          </Dropdown>
        </>
      ))
    }
  </div>;
};

export default InputHelper;
