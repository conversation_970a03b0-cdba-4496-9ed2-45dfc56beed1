import { BOTTOM_ITEM_ID, DRAWER_ID } from '@src/contants';

export const scrollIntoView = () => {
  const bottomItem = document.getElementById(BOTTOM_ITEM_ID);
  if (bottomItem) {
    bottomItem?.scrollIntoView({ behavior: 'smooth' });
  }
};

/**
 * 将数据转换为Markdown表格字符串
 * @param {Object} data - 包含维度和源数据的对象
 * @returns {string} - Markdown格式的表格字符串
 */
export function convertToMarkdownTable(data) {
  const { dimensions = [] } = data;
  const { source = [] } = data;

  // 创建表头
  let markdownTable = `| ${dimensions?.join(' | ')} |\n`;
  markdownTable += `| ${dimensions?.map(() => '---').join(' | ')} |\n`;

  // 添加每一行的数据
  source.forEach((row) => {
    markdownTable += `| ${dimensions?.map((dim) => row?.[dim])?.join(' | ')} |\n`;
  });

  return markdownTable;
}

export function extractSqlTags(str, sign) {
  const regex = new RegExp(`${sign}([\\s\\S]*?)${sign}`, 'g');
  let match;
  const extracted = [];

  // 使用 while 循环手动迭代所有匹配项
  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(str)) !== null) {
    try {
      const data = match[1].trim();
      extracted.push(data);
    } catch (e) {
      console.error(e.message);
      return { extracted: [], remaining: `数据异常: ${match[1]}` };
    }
  }

  // 使用 replace 方法移除所有 <echart> 标签及其内容
  const remaining = str?.replace(regex, '') ?? '';
  return { extracted, remaining };
}

export const getUrlParam = (paramName: string) => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(paramName);
};

// 获取字符串中括号中的内容
export const getBracketContent = (str: string) => {
  const reg = /\(([^()]*)\)/;
  const result = str.match(reg);
  return result ? result[1] : '';
};

export function isNumber(value) {
  return typeof value === 'number' && !Number.isNaN(value);
}

export const getOptions = (type: string, realChartData, title: string) => {
  const { dimensions = [] } = realChartData;
  const unit = getBracketContent(dimensions?.[1] ?? '') || '';

  const commonOptions = {
    color: ['#006ef9', '#80FFA5', '#EE6666', '#73C0DE', '#00DDFF', '#37A2FF', '#FF0087', '#FFBF00'],
    title: {
      text: title || '', // 标题文本
      left: 'center', // 水平居中
      top: 'top', // 位于图表顶部
      overflow: 'break',
      textStyle: {
        fontSize: 14, // 标题字体大小
        fontWeight: 'bold', // 标题字体粗细
      },
    },
    legend: {
      type: 'scroll', // 启用滚动类型
      orient: 'horizontal', // 水平排列
      left: 'center', // 水平居中
      bottom: 'bottom', // 位于图表顶部
    },
    textStyle: {
      fontFamily: 'Arial, sans-serif',
      fontSize: 14,
      color: '#333',
    },
    tooltip: {
      enterable: true,
      className: 'chart-tooltip',
      confine: true,
      trigger: type === 'pie' ? 'item' : 'axis',
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ddd',
      borderWidth: 1,
      textStyle: { color: '#333', fontSize: 14 },
      padding: [10, 15],
      extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.1);',
      valueFormatter: (v) => {
        if (!isNumber(v)) {
          return v;
        }
        return `${parseFloat(v.toFixed(2))} ${unit}`;
      },
    },
    dataset: realChartData,
    // animation: true,
    // animationDuration: 1000,
  };

  const lineAreaStyle = (ind) => ({
    color: {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [{
        offset: 0, color: commonOptions?.color?.[ind] ?? '#006ef9', // 0% 处的颜色
      }, {
        offset: 1, color: '#fff', // 100% 处的颜色
      }],
      global: false, // 缺省为 false
    },
  });

  const getSeriesOptions = (type, dim, i) => {
    const seriesCommonOptions = {
      type,
      name: dim,
      encode: { x: 0, y: dim },
      itemStyle: {
        borderRadius: [6, 6, 0, 0],
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0,0,0,0.3)',
        },
      },
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: { width: 2 },
    };
    return type === 'bar' ? seriesCommonOptions : {
      ...seriesCommonOptions,
      areaStyle: lineAreaStyle(i),
    };
  };

  if (type === 'pie') {
    return {
      ...commonOptions,
      series: (realChartData.dimensions).slice(1).map((dim) => ({
        type,
        name: dim,
        // encode: { x: 0, y: dim },
        itemStyle: {
          borderRadius: [6, 6, 0, 0],
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)',
          },
        },
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { width: 2 },
      })),
    };
  }

  return ({
    ...commonOptions,
    xAxis: {
      type: 'category',
      axisLine: { lineStyle: { color: '#999' } },
      axisTick: { show: false },
      axisLabel: { color: '#666', margin: 15 },
      nameTruncate: {
        maxWidth: 100,
        ellipsis: '...',
      },
    },
    yAxis: {
      axisLine: { lineStyle: { color: '#999' } },
      axisTick: { show: false },
      splitLine: { lineStyle: { color: '#eee', type: 'dashed' } },
      axisLabel: { color: '#666' },
    },
    series: (realChartData.dimensions).slice(1).map((dim, i) => getSeriesOptions(type, dim, i)),
  });
};

/**
 * 均匀采样函数，从输入数组中最多取200项，保持元素间间隔均匀。
 * @param {Array} arr - 需要采样的原始数组。
 * @param {number} [maxSample=200] - 采样后数组的最大长度。
 * @returns {Array} - 采样后的新数组，最多包含maxSample项。
 */
export function sampleArray(arr, maxSample = 500) {
  const n = arr.length;

  // 如果原数组长度小于等于最大采样数，直接返回副本
  if (n <= maxSample) {
    return [...arr];
  }

  const result = [];
  const step = Math.ceil(n / maxSample); // 计算步长，向上取整以确保覆盖所有元素

  for (let i = 0; i < n; i += step) {
    result.push(arr[i]);
  }

  // 如果采样结果超过最大采样数，截取前maxSample项
  return result.slice(0, maxSample);
}

/* eslint-disable prefer-template */
/* eslint-disable prefer-exponentiation-operator */
/**
 * 根据 t（0～1）值计算二次贝塞尔曲线的位置
 * @param {number} t 0～1 的进度
 * @param {object} p0 起点 {x, y}
 * @param {object} p1 控制点 {x, y}
 * @param {object} p2 终点 {x, y}
 * @returns {object} 当前点 {x, y}
 */
function getQuadraticBezierXY(t, p0, p1, p2) {
  // eslint-disable-next-line no-restricted-properties
  const x = Math.pow(1 - t, 2) * p0.x + 2 * (1 - t) * t * p1.x + Math.pow(t, 2) * p2.x;
  // eslint-disable-next-line no-restricted-properties
  const y = Math.pow(1 - t, 2) * p0.y + 2 * (1 - t) * t * p1.y + Math.pow(t, 2) * p2.y;
  return { x, y };
}

/**
 * 核心动画函数：根据起始按钮和目标按钮，生成一个动画元素，
 * 元素将沿贝塞尔曲线路径运动，动画结束后自动删除。
 *
 * @param {HTMLElement} startEl 起始按钮元素
 * @param {HTMLElement} endEl 目标按钮元素
 * @param {number} duration 动画时长（毫秒），默认为 1000
 */
export function animateButton(startEl, endEl, duration = 1000) {
  // 获取两个按钮的位置信息（相对于视窗）
  const startRect = startEl.getBoundingClientRect();
  const endRect = endEl.getBoundingClientRect();

  // 计算起点和终点的中心坐标
  const p0 = {
    x: startRect.left + startRect.width / 2,
    y: startRect.top + startRect.height / 2,
  };
  const p2 = {
    x: endRect.left + endRect.width / 2,
    y: endRect.top + endRect.height / 2,
  };

  // 设定控制点：这里取两个坐标水平中点，纵坐标上偏移一定距离产生弧线效果
  const controlOffset = -100; // 根据需要调整弧线高度
  const p1 = {
    x: (p0.x + p2.x) / 2,
    y: Math.min(p0.y, p2.y) + controlOffset,
  };

  // 创建动画中使用的小按钮（元素大小与样式可以根据需要调整）
  const animEl = document.createElement('div');
  animEl.classList.add('animated-button');
  const constant = 5;
  // 将动画元素放置到起点位置，考虑宽高偏移（元素宽高为20px）
  animEl.style.left = (p0.x - constant) + 'px';
  animEl.style.top = (p0.y - constant) + 'px';
  document.body.appendChild(animEl);

  // 动画起始时间
  const startTime = performance.now();

  // 动画函数：根据时间更新动画元素的位置
  function animate() {
    const currentTime = performance.now();
    const elapsed = currentTime - startTime;
    const t = Math.min(elapsed / duration, 1); // t 取值区间 [0,1]
    const pos = getQuadraticBezierXY(t, p0, p1, p2); // 计算当前点

    animEl.style.left = (pos.x - constant) + 'px';
    animEl.style.top = (pos.y - constant) + 'px';

    if (t < 1) {
      requestAnimationFrame(animate);
    } else {
      // 动画结束后，移除动画元素
      document.body.removeChild(animEl);
    }
  }
  requestAnimationFrame(animate);
}

export const getDrawerWidth = () => {
  const drawer = document.getElementsByClassName(DRAWER_ID)?.[0];
  if (!drawer) {
    return 0;
  }
  const drawerHeaderWidth = drawer?.getElementsByClassName('t-drawer__header')?.[0]?.clientWidth ?? drawer?.clientWidth ?? 0;
  return drawerHeaderWidth;
};
