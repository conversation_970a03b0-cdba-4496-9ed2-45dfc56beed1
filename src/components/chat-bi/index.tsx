/**
 * @description ChatBi页面
 */
import React, {
  useEffect, useState, useRef, useMemo,
} from 'react';
import {
  Bubble, message, Dropdown, List,
} from '@tencent/tea-component';
import {
  Cha<PERSON>, ChatOnboarding, ChatSender, Stack,
} from '@tencent/cloud-chat-ui';
import { ConsultIcon } from '@tencent/cloud-chat-ui/dist/kits/icons';
// eslint-disable-next-line import/no-extraneous-dependencies
import { DeepSeekIcon } from '@tencent/cloud-ai-icon';
import { ContentType, MessageType, MessageTypeEnum } from '@/types';
import { EventEnum } from '@src/contants/event';
import {
  BOTTOM_ITEM_ID, MESSAGE_CONTAINER_ID, SQL_SIGN, MessageEventType, MessageEventStatus,
} from '@src/contants';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useDispatch } from 'react-redux';
import { changeCommonData, useCommonSelector } from '@src/store/app-common';
import { sleep } from '@src/utils';
import { v4 as uuidv4 } from 'uuid';
import { app } from '@tencent/tea-app';
import NewChatIcon from '@/assets/svg-component/new-chat.svg';
import AgentLogPng from '@/assets/png/agent-logo.png';
import s from './index.module.scss';
import MessageItem from './components/message-item';
import { getUrlParam, scrollIntoView } from './utils';
import InputHelper from './components/input-helper';
import NodePopover from './components/node-popover';

export interface IotherBiProps {
  defaultMode?: string;
  useExportComponents?: boolean;
  showModeSwitch?: boolean;
  defaultData?: {
    message: string;
    questions: string[];
  },
  chatOnBoardingData?: {
    title?: string;
    subTitle?: string;
    description?: string;
    quickList: {
      title?: string;
      description?: string;
    }[];
  },
  type?: 'drawer' | 'embedded' // 导出应用的形式，drawer 为抽屉式，embedded 为嵌入式
}

interface ChatBiProps {
  apis: AppPluginAPI.PluginAPI & IotherBiProps;
}
let timer;
const ChatBI = (props: ChatBiProps) => {
  const { apis } = props;
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<MessageType[]>([]);
  const [loading, setLoading] = useState(false);
  const cancelRef = useRef(null);
  const currQuestionRef = useRef(-1);
  const [currQUestion, setCurrQuestion] = useState<string>('');
  const [historyQuestions, setHistoryQuestions] = useState<string[]>([]);
  const needScrollRef = useRef(true);
  const defaultMessageRef = useRef([]);
  const scrollRef = useRef(0);
  const scrollerRef = useRef({});

  const setIsInsightMode = (v) => {
    if (loading) {
      return;
    }
    dispatch(changeCommonData({
      isInsightMode: v,
    }));
  };

  const {
    selectedNode, sessionId, chatProducts, model, isInsightMode, chatVisible,
    chatOnBoardingData, userName, pendingMessage,
  } = useCommonSelector();
  const chatOnboardingProps: any = useMemo(() => {
    const { quickList } = chatOnBoardingData;
    return {
      ...chatOnBoardingData,
      quickList: quickList.map((item) => ({
        ...item,
        icon: <ConsultIcon />,
        onClick: () => {
          handleSendMessage(item?.description);
        },
      })),
    };
  }, [chatOnBoardingData, isInsightMode, selectedNode, loading]);

  useEffect(() => {
    apis?.pluginReportV2({
      event: 'visitChatMode',
      stringFields: {
        mode: isInsightMode ? 'insight' : 'chat',
      },
    });
  }, [isInsightMode]);

  const dispatch = useDispatch();

  useEffect(() => {
    if (pendingMessage.trim()) {
      handleSendMessage(pendingMessage);
      dispatch(changeCommonData({
        pendingMessage: '',
      }));
    }
  }, [pendingMessage]);

  // 把最新的消息展示出来
  useEffect(() => {
    if (!loading) {
      sleep(50).then(() => {
        bottomScroll();
      });
    } else {
      bottomScroll();
    }
  }, [messages, loading]);

  // 允许大模型吐消息的时候滚动查看上面的消息
  useEffect(() => {
    // 监听id为MESSAGE_CONTAINER_ID的容器滚动事件
    const handleScroll = () => {
      const container = document.getElementById(MESSAGE_CONTAINER_ID);
      if (container) {
        const { scrollTop, scrollHeight, clientHeight } = container;
        if (scrollTop < scrollRef.current) {
          needScrollRef.current = false;
        }
        if ((scrollTop + clientHeight + 0.5) >= scrollHeight) {
          needScrollRef.current = true;
        }
        scrollRef.current = scrollTop;
      }
    };
    sleep(100).then(() => {
      const container = document.getElementById(MESSAGE_CONTAINER_ID);
      if (container) {
        container.addEventListener('scroll', handleScroll);
      }
    });

    return () => {
      const container = document.getElementById(MESSAGE_CONTAINER_ID);

      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  useEffect(() => {
    if (apis?.defaultMode === 'insight') {
      setIsInsightMode(true);
    }
  }, []);

  const bottomScroll = () => {
    const bottomItem = document.getElementById(BOTTOM_ITEM_ID);
    if (bottomItem && needScrollRef.current) {
      bottomItem?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleClickNode = (e) => {
    if (chatVisible || props?.apis?.type === 'embedded') {
      const { node, event } = e.detail;
      const { key, name } = node;

      if (!chatProducts.includes(name)) {
        const onDestroy = () => {
          apis?.removeBar?.(key);
        };
        if (apis?.useExportComponents) {
          const { clientX: x, clientY: y } = event;
          // 在x,y位置弹出提示
          const dom = document.createElement('div');
          dom.innerHTML = '当前节点不支持AI问答';
          document.body.appendChild(dom);
          dom.style.position = 'absolute';
          dom.style.top = `${y - 50}px`;
          dom.style.left = `${x - 70}px`;
          dom.style.backgroundColor = 'black';
          dom.style.color = 'white';
          dom.style.padding = '10px';
          dom.style.borderRadius = '5px';
          dom.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';

          sleep(1000).then(() => {
            document.body.removeChild(dom);
          });
        } else {
          apis?.createBar([key], { children: <NodePopover content="当前节点不支持AI问答" onDestroy={onDestroy} /> });
        }
        return;
      }
      dispatch(changeCommonData({
        selectedNode: key,
        drawerVisible: true,
      }));
    }
  };

  // 为选中节点添加闪动样式
  useEffect(() => {
    apis?.removeAllNodeClass?.();
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    if (selectedNode) {
      apis?.addNodeClass(selectedNode, 'shine-node');
      timer = setTimeout(() => {
        apis?.removeAllNodeClass?.();
        apis?.addNodeClass(selectedNode, 'color-node');
      }, 3000);
    }
  }, [selectedNode]);

  useEffect(() => {
    if (!chatVisible) {
      dispatch(changeCommonData({
        selectedNode: undefined,
      }));
    }
  }, [chatVisible]);

  useEffect(() => {
    window.addEventListener(EventEnum.CLICK_NODE, handleClickNode);

    return () => {
      window.removeEventListener(EventEnum.CLICK_NODE, handleClickNode);
      apis?.removeAllNodeClass?.();
    };
  }, [chatProducts, chatVisible]);

  // 处理sse异常
  const handleError = (str?: string, ctrl?: any) => {
    setLoading(false);
    setMessages((prev) => {
      const preList = prev[prev.length - 1]?.contentList ?? [];
      preList[preList.length - 1] = `${preList[preList.length - 1]}
${(str || '查询失败,请稍后再试')}`;
      return [...(prev.slice(0, prev.length - 1)), {
        ...prev[prev.length - 1],
        type: MessageTypeEnum.bot,
        contentType: ContentType.text,
        isInsight: isInsightMode,
        status: 'error',
        errorText: str || '查询失败,请稍后再试',
      }];
    });
    ctrl?.abort?.();
  };

  // 发送消息前对messages预处理
  const handleInitSendMessage = (v: string, options?: {
    addLast?: boolean; // 是否追加，处理查询sql异常场景
    error?: string; // 报错信息
  }) => {
    const { addLast = false } = options || {};
    // 正常消息
    if (!addLast) {
      setHistoryQuestions((prev) => [...prev, v]);
      currQuestionRef.current = historyQuestions.length + 1;
      setMessages((prev) => [...prev, {
        type: MessageTypeEnum.user,
        contentType: ContentType.text,
        id: uuidv4(),
        contentList: [v],
      }, {
        id: uuidv4(),
        type: MessageTypeEnum.bot,
        contentType: ContentType.text,
        isInsight: isInsightMode,
        contentList: [],
        status: 'loading',
        loadingText: '问题理解中...',
        currentNodeKey: selectedNode,
      }]);
    // 处理报错重试
    } else {
      setMessages((prev) => {
        const { contentList } = prev[prev.length - 1];
        // 获取处理过的最后一条数据
        // @ts-ignore
        const currLastMessage = contentList[contentList.length - 1].replaceAll(SQL_SIGN, `
        \`\`\`
        `);
        // 整理最后一条消息
        const showLastMessage = `${currLastMessage}

数据查询异常，正在为您重新查询，请稍等...

`;
        return [...(prev.slice(0, prev.length - 1)), {
          type: MessageTypeEnum.bot,
          contentType: ContentType.text,
          id: prev[prev.length - 1].id,
          isInsight: isInsightMode,
          contentList: [showLastMessage],
          status: 'normal',
          currentNodeKey: selectedNode,
        }];
      });
    }
  };

  // 处理接收到的消息
  const handleReciveMessage = (params: {
    rsp: {data: string;event?: string};
    ctrl: any;
    addLast?: boolean;
  }) => {
    const { rsp, ctrl } = params;
    const isConsole = apis?.env === 'CONSOLE';
    const realData = !isConsole ? rsp?.data.replace('data:', '') : rsp?.data;
    // 数据总是以data:开头，所以要去掉前五位
    try {
      const data = JSON.parse(realData);
      const {
        content,
        is_final: isFInal, // 结束标识
        recommends, // 推荐的问题
        reasoning_content: reasoningContent, // 推理内容
        request_id: requestId, // 请求id
        Response: response,
        delta,
        agent_name: agentName,
        event, // 消息事件
        event_status: eventStatus, // 事件状态
        extra,
      } = data;
      // 处理控制台的异常
      if (isConsole && response?.Error) {
        handleError(response?.Error?.Message, ctrl);
        return;
      }
      const currContent = content ?? delta?.content ?? (agentName ? `
` : '');
      setMessages((prev) => {
        const preList = prev[prev.length - 1]?.contentList ?? [];
        const matchLength = preList[preList.length - 1]?.match(/```/g)?.length;
        // 如果已经有两个了，那新加一项
        if (currContent.includes('```') && (matchLength > 1 || !matchLength)) {
          preList.push(currContent);
        } else if (currContent.includes('```') && matchLength === 1) {
          preList[preList.length - 1] = `${preList[preList.length - 1]}${currContent}`;
        } else if (preList[preList.length - 1]?.endsWith('```') && matchLength > 1) {
          preList.push('');
        } else if (preList.length) {
          preList[preList.length - 1] = `${preList[preList.length - 1]}${currContent}`;
        } else {
          preList.push(currContent);
        }
        if (event === MessageEventType.governTopicThinking && eventStatus === MessageEventStatus.end) {
          return [...(prev.slice(0, prev.length - 1)), {
            ...prev[prev.length - 1],
            type: MessageTypeEnum.bot,
            contentType: ContentType.text,
            reasoningContent: (prev[prev.length - 1].reasoningContent ?? '') + (reasoningContent ?? ''),
            requestId,
            isInsight: isInsightMode,
            contentList: preList,
            status: 'normal',
            event,
            eventStatus,
            extra,
          }, {
            id: uuidv4(),
            type: MessageTypeEnum.bot,
            contentType: ContentType.text,
            isInsight: isInsightMode,
            contentList: [],
            status: 'normal',
            currentNodeKey: selectedNode,
          }];
        }
        return [...(prev.slice(0, prev.length - 1)), {
          ...prev[prev.length - 1],
          type: MessageTypeEnum.bot,
          contentType: ContentType.text,
          reasoningContent: (prev[prev.length - 1].reasoningContent ?? '') + (reasoningContent ?? ''),
          requestId,
          isInsight: isInsightMode,
          contentList: preList,
          status: 'normal',
          event,
          eventStatus,
          extra,
        }];
      });
      if (isFInal) {
        setLoading(false);
        ctrl.abort();
        setMessages((prev) => [...(prev.slice(0, prev.length - 1)), {
          ...prev[prev.length - 1],
          questions: recommends,
          isInsight: isInsightMode,
          status: 'normal',
        }]);
      }
    } catch (e) {
      // handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  // 处理发送消核心逻辑
  const handleSendMessage = (v: string, options?: {
    addLast?: boolean; // 是否追加，处理查询sql异常场景
    error?: string; // 报错信息
  }) => {
    if (!v.trim()) return;
    // 如果正在回答中，则不处理
    if (loading) {
      message.error({ content: '回答输出中，请稍后操作或点击停止回答' });
      return;
    }
    setCurrQuestion(v);
    setLoading(true);
    const { addLast = false, error = '' } = options || {};
    setInputValue('');
    handleInitSendMessage(v, options);
    scrollIntoView();
    needScrollRef.current = true;
    // 控制取消请求
    const ctrl = new AbortController();
    cancelRef.current = ctrl;
    const question = error ? `${v}？
这个问题的sql查询数据失败，错误信息是：
${error}
分析异常重新查询一次` : v;
    const isConsole = apis?.env === 'CONSOLE';
    let url;
    let method;
    let headers;
    let body;
    if (isConsole) {
      const insightParams = {
        Version: '2025-03-17',
        ArchId: apis?.archInfo?.archId,
        NodeUUID: selectedNode,
        // OutputType: 'append',
        Question: question,
        SessionID: sessionId,
        UserName: userName,
      };
      const params = {
        ...insightParams,
        OutputType: 'append',
      };
      const info = app.capi.generateSseRequest({
        serviceType: 'saai',
        cmd: isInsightMode ? 'ChatInsightCompletions' : 'ChatBICompletions',
        data: isInsightMode ? insightParams : params,
        regionId: 1,
      });
      const {
        url: u, method: m, headers: h, body: b,
      } = info;
      url = u;
      method = m;
      headers = h;
      body = b;
    } else {
      const params = {
        ArchId: apis?.archInfo?.archId,
        NodeUUID: selectedNode,
        AppId: apis?.appId ?? Number(getUrlParam('appid')),
        OutputType: 'append',
        Question: question,
        SessionID: sessionId,
        Uin: apis?.uin,
        Action: isInsightMode ? 'ChatInsight' : 'GetChatMessage',
        ApiSource: model,
        UserName: userName,
      };
      url = '/1/sse';
      method = 'post';
      headers = {
        'Content-Type': 'text/event-stream',
        StaffName: userName,
      };
      body = JSON.stringify(params);
    }
    fetchEventSource(url, {
      method,
      headers,
      body,
      credentials: 'include',
      openWhenHidden: true,
      signal: ctrl.signal,
      // 必须设置，否则出现异常无法终止
      onopen(res): any {
        if (res.status !== 200) {
          handleError('网络异常', ctrl);
        } else if (!addLast) {
          setMessages((prev) => [...(prev.slice(0, prev.length - 1)), {
            type: MessageTypeEnum.bot,
            contentType: ContentType.text,
            id: prev[prev.length - 1]?.id,
            isInsight: isInsightMode,
            contentList: [],
            status: 'loading',
            loadingText: '问题理解中...',
            currentNodeKey: selectedNode,
          }]);
        }
      },
      onmessage(rsp) {
        handleReciveMessage({
          rsp,
          ctrl,
          addLast,
        });
      },
      onerror(e) {
        handleError(e.message, ctrl);
        console.error('sse error', e, ctrl);
      },
    });
  };

  const isAllEmptyArray = (arr) => {
    const isAllEmpty = arr?.every?.((str) => str?.trim() === '' || str?.trim() === '\n');
    return isAllEmpty;
  };

  const stopMessage = () => {
    if (!loading) return;
    cancelRef.current?.abort();
    apis?.pluginReportV2({
      event: 'stopChat',
      stringFields: {
        sessionId,
      },
    });
    setLoading(false);
    sleep(50).then(() => {
      scrollIntoView();
    });
    setMessages((prev) => [...(prev.slice(0, prev.length - 1)), {
      ...prev[prev.length - 1],
      status: prev[prev.length - 1]?.status === 'loading' ? 'normal' : prev[prev.length - 1]?.status,
      contentList: isAllEmptyArray(prev[prev.length - 1]?.contentList)
        ? ['云巡检Agent已停止执行此操作，如有需求可以继续向我提问'] : prev[prev.length - 1]?.contentList,
      // eslint-disable-next-line max-len
      thinkingStop: prev[prev.length - 1]?.event === MessageEventType.governTopicThinking ? true : undefined, // 如果是深度思考过程停止
    }]);
  };
  const chatContent = useMemo(() => (
    <>
      {
        messages?.length === 0 && (
          <ChatOnboarding
            title={(
              <div className={s['agent-logo']}>
                <img className={s.logo} src={AgentLogPng} alt="" />
                <span className={s.text}>你好</span>
              </div>
            )}
            {...chatOnboardingProps}
          />
        )
      }
      {
        !!messages?.length && (
          <Stack>
            {
              messages?.map((item, ind) => <MessageItem
                loading={loading}
                item={item}
                key={item?.id}
                handleSendMessage={handleSendMessage}
                isLatest={ind === messages.length - 1}
                isFirst={ind === 0}
                sessionId={sessionId}
                question={currQUestion}
                apis={apis}
                model={model}
                isInsight={item?.isInsight}
              />)
            }
          </Stack>
        )
      }
    </>
  ), [messages, loading, sessionId, currQUestion, apis, model, selectedNode, userName, handleSendMessage]);

  useEffect(() => {
    window.addEventListener(EventEnum.NEW_CHAT, handleNewChat);

    return () => {
      window.removeEventListener(EventEnum.NEW_CHAT, handleNewChat);
    };
  }, [messages]);

  const handleNewChat = () => {
    if (messages.length <= 1) {
      message.success({ content: '已经是最新的会话' });
      return;
    }
    cancelRef.current?.abort();
    setLoading(false);
    setMessages(defaultMessageRef.current);
    const newSessionId = uuidv4();
    apis?.pluginReportV2({
      event: 'createChat',
      stringFields: {
        sessionId: newSessionId,
      },
    });
    dispatch(changeCommonData({
      sessionId: newSessionId,
    }));
  };

  const handleHelperWords = (v: string) => {
    setInputValue((pre) => {
      const noPre = !pre.trim() || !pre;
      const lastWordN = pre[pre.length - 1] === '\n' ? '' : ' ';
      return noPre ? v : `${pre}${lastWordN}${v}`;
    });
  };

  const getSize = () => {
    let preSize = localStorage.getItem('ai-bi-for-inspection-drawer-size');
    if (!preSize || preSize === 'NaN') {
      preSize = `${500}`;
    }
    return Number(preSize);
  };

  const setSize = (size: number) => {
    localStorage.setItem('ai-bi-for-inspection-drawer-size', size.toString() === 'NaN' ? '500' : size.toString());
  };
  return (
    <Chat
      className={s['agent-chat']}
      title="风险分析"
      visible={chatVisible}
      isInSessions={messages.length > 0}
      fixedTop={50}
      minWidth={500}
      maxWidth={800}
      type={props?.apis.type}
      // @ts-ignore
      defaultSize={{
        width: getSize(),
      }}
      onSizeChange={(size) => {
        setSize(size?.width);
      }}
      onCloseButtonClick={() => {
        dispatch(changeCommonData({
          chatVisible: false,
        }));
      }}
      bounds={{
        top: 100, left: 100, right: 10, bottom: 30,
      }}
      scrollerRef={scrollerRef}
      inputArea={
        <>
          <ChatSender
            loading={loading}
            value={inputValue?.length > 0 && !inputValue?.trim() ? '' : inputValue}
            onChange={(v) => setInputValue(v)}
            // eslint-disable-next-line consistent-return
            onSend={(v) => {
              if (v.trim().length <= 0) {
                return message.warning({
                  content: '请勿使用空白信息进行询问',
                });
              }
              handleSendMessage(v);
            }}
            onCancelProcess={() => {
              stopMessage();
            }}
            topExtra={
              <>
                <InputHelper apis={apis} handleHelperWords={handleHelperWords} focusInput={() => {}} />
              </>
            }
            bottomLeftExtra={
              apis?.env !== 'CONSOLE'
                ? (
                  <>
                    <Dropdown
                      clickClose={false}
                      className={s['channel-change-dropdown']}
                      button={(
                        <div>
                          <DeepSeekIcon />
                          {' '}
                          {model === 'channel1' ? 'DeepSeek通道一' : 'DeepSeek通道二'}
                        </div>
                    )}
                      appearance="button"
                    >
                      {(close) => (
                        <List type="option">
                          <List.Item
                            onClick={() => {
                              dispatch(changeCommonData({
                                model: 'channel1',
                              }));
                              close();
                            }}
                          >
                            <DeepSeekIcon />
                            {' '}
                            DeepSeek通道一
                          </List.Item>
                          <List.Item
                            onClick={() => {
                              dispatch(changeCommonData({
                                model: 'channel2',
                              }));
                              close();
                            }}
                          >
                            <DeepSeekIcon />
                            {' '}
                            DeepSeek通道二
                          </List.Item>
                        </List>
                      )}
                    </Dropdown>
                  </>
                ) : null
            }
            bottomRightExtra={
              <Bubble content="新建会话">
                <NewChatIcon
                  // @ts-ignore
                  style={{ cursor: 'pointer' }}
                  size={24}
                  onClick={() => window.dispatchEvent(new CustomEvent(EventEnum.NEW_CHAT))}
                />
              </Bubble>
            }
          />
        </>
      }
    >
      {chatContent}
    </Chat>
  );
};

export default ChatBI;
