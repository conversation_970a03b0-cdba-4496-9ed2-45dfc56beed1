import React, {
  useEffect, useMemo, useRef, useState,
} from 'react';
import GridLayout from 'react-grid-layout';
import AutoSizer from 'react-virtualized-auto-sizer';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { useDispatch } from 'react-redux';
import { AddIcon, LoadingIcon } from '@tencent/tea-icons-react';

import {
  Bubble, Checkbox, Icon, message,
} from '@tencent/tea-component';
import { DashboardItemTypeEnum, MAX_CHART_COUNT, MAX_CHART_TIP } from '@src/contants';
import { debounce, cloneDeep } from 'lodash';
import {
  addGraphToDashboardFinal, deleteDashboardGraphFinal, getDashboardDataFinal, updateDashBoardDataFinal,
} from '@src/utils/get-data';
import RenderItem from './components/render-item/index';

import cs from './index.module.scss';
import { getUrlParam } from '../chat-bi/utils';

interface DashboardProps {
  apis: AppPluginAPI.PluginAPI;
}
let autoRefreshTimer;

export default function Dashboard({ apis }: DashboardProps): React.ReactElement {
  const [layouts, setLayouts] = useState([]);
  const {
    dashBoardId, boardVisible, autoRefresh, dashboardList, dashBoardName, changingGraphMap, dashboardItemCount,
  } = useCommonSelector();
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [refreshTime, setRefreshTime] = useState(0);
  const [addLoading, setAddLoading] = useState(false);
  const layoutRef = useRef(null);
  const [isEnd, setIsEnd] = useState(false);
  const dispatch = useDispatch();
  const listRef = useRef([]);
  const isConsole = apis?.env === 'CONSOLE';

  useEffect(() => {
    listRef.current = dashboardList;
  }, [dashboardList]);

  const setList = (list) => {
    dispatch(changeCommonData({
      dashboardList: list,
    }));
  };

  const getBaseParams = () => ({
    AppId: apis?.appId ?? Number(getUrlParam('appid')),
    ArchId: apis?.archInfo?.archId,
    Uin: apis.uin,
  });

  // 需用listRef.curren获取最新数据
  const handleLayoutChange = debounce((layouts: Array<any>) => {
    const newDashBoardLists = [];
    layouts.forEach((layout) => {
      const index = listRef.current.findIndex((dash) => dash.GraphId === layout.i);
      if (index > -1) {
        const listItem = listRef.current[index];
        const item = {
          ...listItem,
          Position: [layout.x, layout.y, layout.w, layout.h],
          GraphType: listItem.GraphType,
        };
        newDashBoardLists.push(item);
      }
    });
    setList(newDashBoardLists);
    if (listRef.current.length === layouts.length - 1) {
      updateList(newDashBoardLists);
    }
  }, 100);

  const updateList = debounce((newDashBoardLists, cb?) => {
    updateDashBoardDataFinal({
      isConsole,
      apiParams: {
        ...getBaseParams(),
        DashBoardId: dashBoardId,
        GraphItems: newDashBoardLists,
        DashBoardName: dashBoardName,
      },
    }).then(() => {
      // setList(newDashBoardLists);
      if (cb) {
        cb?.();
      }
    });
  }, 1000);

  const handleDeleteItem = (graphId: string) => {
    const lists = [...listRef.current];
    const index = lists.findIndex((item) => item.GraphId === graphId);
    if (index > -1) {
      lists.splice(index, 1);
      setList(lists);
      deleteDashboardGraphFinal({
        isConsole,
        apiParams: {
          AppId: apis?.appId ?? Number(getUrlParam('appid')),
          Uin: apis.uin,
          ArchId: apis?.archInfo?.archId,
          DashBoardId: dashBoardId,
          GraphId: [graphId],
        },
      });
    }
  };

  const handleChange = (graphId: string, type: 'delete' | 'change', args?: { value: any }) => {
    if (type === 'delete') {
      handleDeleteItem(graphId);
      return;
    }
    if (type === 'change') {
      const index = dashboardList.findIndex((dash) => dash.GraphId === graphId);
      if (index === -1) {
        setList(dashboardList);
        updateDashBoardDataFinal({
          isConsole,
          apiParams: {
            ...getBaseParams(),
            DashBoardId: dashBoardId,
            GraphItems: dashboardList,
            DashBoardName: args.value,
          },
        }).then(() => {
          // setList(dashboardList);
        });
      } else {
        const newDashboardList = [...dashboardList];
        const item = newDashboardList[index];
        if (item.GraphType === 'text-graph') {
          newDashboardList[index] = {
            ...item,
            DataText: args.value,
          };
          setList(newDashboardList);
          updateDashBoardDataFinal({
            isConsole,
            apiParams: {
              ...getBaseParams(),
              DashBoardId: dashBoardId,
              GraphItems: newDashboardList,
              DashBoardName: dashBoardName,
            },
          }).then(() => {
            // setList(newDashboardList);
          });
        }
      }
    }
  };

  const getBoardData = () => {
    setRefreshTime(Date.now());
    setRefreshLoading(true);
    return getDashboardDataFinal({
      isConsole,
      apiParams: {
        ...getBaseParams(),
        DashBoardId: dashBoardId,
      },
    }).then(({ GraphItems, DashBoardName }) => {
      setList([...GraphItems]);
      dispatch(changeCommonData({
        dashBoardName: DashBoardName || '默认看板',
      }));
    }).finally(() => {
      setRefreshLoading(false);
    });
  };

  /**
   *
   * @param position xywh为新text的position，i是当前操作图表id，dir是生成方向
   * @returns
   */
  const handleAddText = (position?: {x?: number, y?: number, w?: number, h?: number, i?: string, dir?: string}) => {
    if (addLoading) return;
    if (dashboardItemCount >= MAX_CHART_COUNT) {
      message.warning({ content: MAX_CHART_TIP });
      return;
    }
    const addItem = () => {
      setAddLoading(true);
      const item = {
        ...getBaseParams(),
        DashBoardId: dashBoardId,
        GraphDesc: '',
        GraphName: '',
        DataText: 'text',
        Position: [position?.x ?? 0, position?.y ?? 2, position?.w ?? 6, position?.h ?? 3],
        GraphType: DashboardItemTypeEnum.text,
        ChartOptions: JSON.stringify({}),
        DiagramId: '',
      };
      addGraphToDashboardFinal({ apiParams: item, isConsole }).then(({ GraphId }) => {
        const newList = cloneDeep(dashboardList);
        setList([
          ...newList,
          {
            ...item,
            GraphId: GraphId ?? `text-${new Date().getTime()}`,
          },
        ]);
      }).finally(() => {
        setAddLoading(false);
      });
    };
    addItem();
  };

  useEffect(() => {
    if (dashBoardId && boardVisible) {
      getBoardData().finally(() => setIsEnd(true));
    }
  }, [dashBoardId, boardVisible]);

  useEffect(() => {
    const resizeHandles = ['sw', 'nw', 'se', 'ne'];
    const layouts = dashboardList.map((item) => {
      if (item.GraphType === DashboardItemTypeEnum.sql) {
        try {
          const option = JSON.parse(item.ChartOptions);

          return {
            type: 'chart',
            i: item.GraphId,
            x: item.Position[0],
            y: item.Position[1],
            w: item.Position[2],
            h: item.Position[3],
            option,
            sql: item?.DataSetSQL,
            title: item?.GraphName ?? '',
            nodeId: item?.DiagramId,
            nodeStatus: item?.NodeStatus,
            isText: false,
            nodeName: item?.NodeName,
            nodeType: item?.NodeType,
            resizeHandles,
            minW: 3,
            minH: 4,
          };
        } catch (e) {
          console.error(e);
        }
      }

      return {
        type: 'text',
        i: item.GraphId,
        x: item.Position[0],
        y: item.Position[1],
        w: item.Position[2],
        h: item.Position[3],
        title: item?.DataText ?? '',
        isDraggable: true,
        isResizable: true,
        nodeId: item?.DiagramId,
        nodeStatus: item?.NodeStatus,
        isText: true,
        resizeHandles,
        minW: 2,
        minH: 2,
      };
    });
    if (dashBoardName) {
      // 默认大标题
      layouts.unshift({
        type: 'text',
        i: 'dashBoardName',
        title: dashBoardName,
        x: 0,
        y: 0,
        w: 12,
        h: 3,
        // @ts-ignore
        isDraggable: false,
        isResizable: false,
        nodeId: '',
        nodeStatus: '',
        isText: false,
      } as any);
    }
    setLayouts(layouts);
  }, [dashboardList, dashBoardName]);

  useEffect(() => {
    if (autoRefresh && dashBoardId && boardVisible) {
      autoRefreshTimer = setInterval(() => {
        getBoardData();
      }, 30 * 1000);
    }
    return () => {
      clearInterval(autoRefreshTimer);
    };
  }, [autoRefresh, dashBoardId, boardVisible]);

  const handleRefresh = () => {
    if (!canRefresh) return;
    getBoardData();
  };

  // 有正在更新中的图阻止刷新
  const canRefresh = useMemo(() => {
    const hasChangingGraph = Object.values(changingGraphMap).some((item) => item);
    return !hasChangingGraph;
  }, [changingGraphMap]);

  const handleCreateText = (item, dir) => {
    if (dashboardItemCount >= MAX_CHART_COUNT) {
      message.warning({ content: MAX_CHART_TIP });
      return;
    }
    const {
      x, y, w, i,
    } = item;
    const nextX = x;
    let nextY = y;
    const newW = w;
    if (dir === 'top') {
      nextY = y - 1;
    }
    if (dir === 'bottom') {
      nextY = y + 1;
    }
    handleAddText({
      x: nextX, y: nextY, w: newW, i, dir,
    });
  };

  return (
    <div className={cs.right}>
      <div className={cs.options}>
        <Bubble content="开启每30秒刷新数据" tooltip>
          <Checkbox
            name="autoRefresh"
            value={autoRefresh}
            onChange={(v) => dispatch(changeCommonData({ autoRefresh: v }))}
            disabled={!canRefresh}
          >
            自动刷新
          </Checkbox>
        </Bubble>

        {refreshLoading
          ? <Icon type="loading" size="s" />
          : <Bubble content={canRefresh ? '刷新数据' : '有图表更新中，请更新完成后重试'} tooltip>
            <Icon type="refresh" className={cs.icon} size="s" onClick={handleRefresh} />
          </Bubble>}

        <span className={cs.add} onClick={() => handleAddText()}>
          <Bubble content="添加文本" tooltip>
            {
              addLoading ? <LoadingIcon size={22} /> : <AddIcon size={22} />
            }
          </Bubble>
        </span>
      </div>
      <div className={cs.layout} ref={layoutRef}>
        {isEnd ? <AutoSizer>
          {({ width }) => (
            <GridLayout
              className="layout"
              id="dashboard-layout"
              layout={layouts}
              cols={12}
              rowHeight={20}
              width={width}
              onLayoutChange={handleLayoutChange}
              draggableHandle=".drag-handle"
            >
              {
                layouts.map((item) => (
                  <div key={item.i} className={cs.item} onClick={() => console.log('item', item)}>
                    {
                      item.type === 'chart' && (
                        <>
                          <div
                            className={`${cs.circle} ${cs.tc}`}
                            onClick={() => handleCreateText(item, 'top')}
                          >
                            +
                          </div>
                          <div
                            className={`${cs.circle} ${cs.bc}`}
                            onClick={() => handleCreateText(item, 'bottom')}
                          >
                            +
                          </div>
                        </>
                      )
                    }
                    <RenderItem
                      title={item.title}
                      option={item.option}
                      type={item.type}
                      onChange={(type, args) => handleChange(item.i, type, args)}
                      sql={item.sql}
                      refreshTime={refreshTime}
                      apis={apis}
                      nodeId={item.nodeId}
                      nodeStatus={item.nodeStatus}
                      isText={item.isText}
                      graphId={item.i}
                      nodeInfo={{
                        nodeName: item.nodeName,
                        nodeType: item.nodeType,
                      }}
                    />
                  </div>
                ))
              }
            </GridLayout>
          )}
        </AutoSizer> : null}

      </div>
    </div>
  );
}
