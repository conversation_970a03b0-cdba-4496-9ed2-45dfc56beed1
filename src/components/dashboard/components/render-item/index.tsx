import React from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import AutoSizer from 'react-virtualized-auto-sizer';
import { DashboardItemValid } from '@src/contants';
import Chart from './components/chart';
import Text from './components/text';

import cs from './index.module.scss';
import CreateComponent from './components/create-component';

interface RenderItemProps {
  title: string;
  type: string;
  option?: any;
  onChange: (type: 'delete' | 'change', args?: { value: any }) => void;
  sql: string;
  refreshTime?: number;
  apis: AppPluginAPI.PluginAPI;
  nodeId: string;
  nodeStatus: DashboardItemValid;
  isText?: boolean;
  graphId?: string;
  nodeInfo: {
    nodeName: string;
    nodeType: string;
  }
}

export default function RenderItem({
  type,
  title,
  option,
  sql,
  refreshTime,
  onChange,
  apis,
  nodeId,
  nodeStatus,
  isText,
  graphId,
  nodeInfo,
}: RenderItemProps): React.ReactElement {
  if (type === 'chart') {
    return (
      <div className={cs.item}>
        <AutoSizer className={cs['item-autosizer']}>
          {({ width, height }) => (
            <Chart
              title={title}
              option={option}
              width={width}
              height={height}
              onChange={onChange}
              sql={sql}
              refreshTime={refreshTime}
              apis={apis}
              nodeId={nodeId}
              nodeStatus={nodeStatus}
              graphId={graphId}
              nodeInfo={nodeInfo}
            />
          )}
        </AutoSizer>
      </div>
    );
  }

  if (type === 'text') {
    return (
      <div className={cs.item}>
        <AutoSizer className={cs['item-autosizer']}>
          {({ width, height }) => (
            <Text
              width={width}
              height={height}
              title={title}
              type={type}
              onChange={onChange}
              isText={isText}
            />
          )}
        </AutoSizer>
      </div>
    );
  }

  return (
    <div className={cs.item}>
      <AutoSizer className={cs['item-autosizer']}>
        {({ width, height }) => (
          <CreateComponent
            width={width}
            height={height}
            apis={apis}
          />
        )}
      </AutoSizer>
    </div>
  );
}
