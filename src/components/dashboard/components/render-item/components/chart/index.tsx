/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect, useRef, useState } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import { DeleteIcon, EditIcon } from '@tencent/tea-icons-react';

import ChartRender from '@src/components/chat-bi/components/chart-render';
import { changeCommonData, useCommonSelector } from '@src/store/app-common';
import {
  Bubble, Button, Icon, Input, PopConfirm,
} from '@tencent/tea-component';
import { getDrawerWidth, getUrlParam } from '@src/components/chat-bi/utils';
import { v4 as uuidv4 } from 'uuid';
import { CHANGE_INPUT_TIP, DashboardItemValid, INPUT_MAX_LENGTH } from '@src/contants';
import { useDispatch } from 'react-redux';
import { cloneDeep } from 'lodash';
import { sleep } from '@src/utils';
import {
  deleteDashboardGraphFinal, getEchartData, redraw<PERSON>hart, updateDashBoardDataFinal,
} from '@src/utils/get-data';
import cs from './index.module.scss';

interface RenderItemProps {
  option: any;
  title?: string;
  width: number;
  height: number;
  onChange: (type: 'delete' | 'change', args?: { value: any }) => void;
  sql: string;
  refreshTime?: number;
  apis: AppPluginAPI.PluginAPI;
  nodeId: string;
  nodeStatus: DashboardItemValid;
  graphId?: string;
  nodeInfo: {
    nodeName: string;
    nodeType: string;
  }
}

export default function Chart({
  option,
  width,
  height,
  onChange,
  sql,
  refreshTime,
  apis,
  nodeId,
  nodeStatus,
  graphId,
  nodeInfo,
}: RenderItemProps): React.ReactElement {
  const {
    sessionId, model, autoRefresh, dashboardList, dashBoardId, dashBoardName, changingGraphMap,
  } = useCommonSelector();
  const dispatch = useDispatch();

  const [chartData, setChartData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [changeInputValue, setChangeInputValue] = useState('');
  const [options, setOptions] = useState(option);
  const [show, setShow] = useState(false);
  const refreshTimeRef = useRef(0);

  const isInvalid = nodeStatus === DashboardItemValid.invalid;
  const isConsole = apis?.env === 'CONSOLE';

  useEffect(() => {
    if (!show || refreshTimeRef.current === refreshTime || isInvalid) return;
    setLoading(true);
    getEchartData({
      isConsole,
      apiParams: {
        Action: 'GetChartDataSet',
        DataSQL: btoa(unescape(encodeURIComponent(sql))),
        SessionId: sessionId,
        AppId: apis?.appId ?? Number(getUrlParam('appid')),
        ArchId: apis?.archInfo?.archId,
        NodeUUID: nodeId,
      },
    }).then((d) => {
      if (d) {
        try {
          const data = JSON.parse(d?.DataSet);
          setChartData(data);
          // 记录这份data的refreshTime
          refreshTimeRef.current = refreshTime;
        } catch (e) {
          console.error(e, d);
          setChartData({ title: { text: '数据解析错误' } });
        }
      }
    }).finally(() => {
      setLoading(false);
    });
  }, [refreshTime, show]);

  useEffect(() => {
    sleep(100).then(() => {
      // 选择目标元素
      const targetDiv = document.getElementById(graphId);
      if (!targetDiv) return;
      // 创建 Intersection Observer 实例
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setShow(true);
          } else {
            setShow(false);
          }
        });
      }, {
        root: document.getElementById('dashboard-layout'), // 默认为视口
        rootMargin: '-50px',
        threshold: 0, // 当目标元素的 50% 进入视口时触发
      });

      // 开始观察目标元素
      observer.observe(targetDiv);
    });
  }, []);

  // 修改图表
  const handleSendChangeMessage = () => {
    dispatch(changeCommonData({ changingGraphMap: { ...changingGraphMap, [graphId]: true } }));
    const preAutoRefresh = autoRefresh;
    dispatch(changeCommonData({ autoRefresh: false }));
    setChangeInputValue('');
    setLoading(true);
    const optionsData = {
      dimensions: chartData?.dimensions,
      source: chartData?.source?.slice(0, 10),
    };
    const optionParam = {
      ...options,
      dataset: optionsData,
    };
    const queryParams = {
      Question: changeInputValue,
      SessionID: uuidv4(),
      Uin: apis?.uin,
      Action: 'RedrawChart',
      ApiSource: model,
      ChartOptions: JSON.stringify(optionParam),
      ArchId: apis?.archInfo?.archId,
      NodeUUID: nodeId,
      AppId: apis?.appId ?? Number(getUrlParam('appid')),
    };

    redrawChart({ apiParams: queryParams, isConsole }).then((d) => {
      try {
        const newOptions = {
          ...JSON.parse(d?.Answer),
          dataset: chartData,
        };
        setOptions(newOptions);
        updataDashboardList(newOptions);
        // 更新list
      } catch (e) {
        console.error(e.message);
      }
    }).finally(() => {
      setLoading(false);
      dispatch(changeCommonData({
        autoRefresh: preAutoRefresh,
        changingGraphMap: { ...changingGraphMap, [graphId]: false },
      }));
    });
  };

  const updataDashboardList = (options: any) => {
    const newList = cloneDeep(dashboardList);
    const currItem = newList.find((item) => item.GraphId === graphId);
    try {
      currItem.ChartOptions = JSON.stringify(options);
      currItem.GraphName = options?.title?.text ?? currItem?.GraphName;
      const lists = [...newList];
      const index = lists.findIndex((item) => item.GraphId === currItem.GraphId);
      if (index > -1) {
        lists.splice(index, 1);
      }

      updateDashBoardDataFinal({
        isConsole,
        apiParams: {
          AppId: apis?.appId ?? Number(getUrlParam('appid')),
          ArchId: apis?.archInfo?.archId,
          Uin: apis.uin,
          DashBoardId: dashBoardId,
          GraphItems: [...lists, currItem],
          DashBoardName: dashBoardName,
        },
      });
    } catch (e) {
      console.error(e.message);
    }
  };

  const changeInput = <div className={cs['change-input']}>
    <Input
      maxLength={INPUT_MAX_LENGTH}
      placeholder={CHANGE_INPUT_TIP}
      value={changeInputValue}
      onChange={setChangeInputValue}
      onPressEnter={handleSendChangeMessage}
      autoFocus
      disabled={loading}
    />
  </div>;

  const handleClickChart = () => {
    const drawerHeaderWidth = getDrawerWidth();
    apis?.zoomNodeToPosition?.(nodeId, { rightWidth: drawerHeaderWidth });
    dispatch(changeCommonData({
      selectedNode: nodeId,
    }));
  };

  if (!show) {
    return <div id={graphId} style={{ width: '100%', height: '100%' }} />;
  }

  const getBaseParams = () => ({
    AppId: apis?.appId ?? Number(getUrlParam('appid')),
    ArchId: apis?.archInfo?.archId,
    Uin: apis.uin,
  });

  const handleDelInvalid = () => {
    const newList = dashboardList.filter((item) => item.NodeStatus !== 'invalid');
    const needDelList = dashboardList?.filter((item) => item?.NodeStatus === 'invalid')?.map((item) => item?.GraphId);
    dispatch(changeCommonData({
      dashboardList: newList,
    }));
    deleteDashboardGraphFinal({
      isConsole,
      apiParams: {
        ...getBaseParams(),
        DashBoardId: dashBoardId,
        GraphId: needDelList,
      },
    });
  };

  return (
    <div
      className={cs.chart}
      onClick={handleClickChart}
      id={graphId}
    >
      <div className={`${cs.drag} drag-handle`} />
      {
        loading && !isInvalid && (
          <div className={cs.loading}>
            <Icon type="loading" />
          </div>
        )
      }
      <div className={cs.content} style={{ width, height }}>
        {
          !isInvalid && (
          <ChartRender
            chartData={!isInvalid ? chartData : { dimensions: [], source: [] }}
            sql={!isInvalid ? sql : ''}
            changeLoading={false}
            options={!isInvalid
              ? options
              : { title: { text: '' } }}
            width={width}
            height={height}
            addon={
              <div className="drag-handle" style={{ width: width - 160, cursor: 'move' }} />
          }
            nodeStatus={nodeStatus}
          />
          )
        }
        {
          isInvalid && <div className={cs.invalid}>
            <div className={cs.title}>{options?.title?.text ?? nodeInfo?.nodeName}</div>
            <div className={cs.desc}>
              {`${nodeInfo.nodeName}(${nodeInfo.nodeType})已经被移除，本图卡已失效，您可以：`}
              <PopConfirm
                title="确定删除该图表？"
                message="删除图表后无法恢复该图表"
                footer={(close) => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        close();
                        onChange('delete');
                      }}
                    >
                      确定
                    </Button>
                    <Button
                      type="text"
                      onClick={() => {
                        close();
                      }}
                    >
                      取消
                    </Button>
                  </>
                )}
                placement="top"
              >
                <span>删除本图卡</span>
              </PopConfirm>
              或者
              <PopConfirm
                title="确定清理所有失效图卡？"
                message="清理所有失效图卡后无法恢复"
                footer={(close) => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        close();
                        handleDelInvalid();
                      }}
                    >
                      确定
                    </Button>
                    <Button
                      type="text"
                      onClick={() => {
                        close();
                      }}
                    >
                      取消
                    </Button>
                  </>
                )}
                placement="top"
              >
                <span>清理所有失效图卡</span>
              </PopConfirm>
            </div>
          </div>
        }
      </div>
      {
        !isInvalid && (
        <div className={cs.tool}>
          <div className={cs['tool-item']}>
            {
            !isInvalid && (
            <PopConfirm
              title="修改样式"
              message={changeInput}
              footer={null}
              placement="bottom"
            >
              <Bubble content="修改样式" tooltip>
                <EditIcon
                  color="#888888"
                  size={14}
                  className={cs['tool-item-btn']}
                />
              </Bubble>
            </PopConfirm>
            )
          }
            <PopConfirm
              title="确定删除该图表？"
              message="删除图表后无法恢复该图表"
              footer={(close) => (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      close();
                      onChange('delete');
                    }}
                  >
                    确定
                  </Button>
                  <Button
                    type="text"
                    onClick={() => {
                      close();
                    }}
                  >
                    取消
                  </Button>
                </>
              )}
              placement="top"
            >
              <Bubble content="删除图表" tooltip>
                <DeleteIcon color="#888888" size={14} className={cs['tool-item-btn']} />
              </Bubble>
            </PopConfirm>

          </div>
        </div>
        )
      }
    </div>
  );
}
