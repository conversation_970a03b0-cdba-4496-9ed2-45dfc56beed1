.chart {
  position: relative;

  .drag {
    position: absolute;
    z-index: 1;
    top: -10px;
    right: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    cursor: move;
  }

  .tool {
    position: absolute;
    z-index: 2;
    top: 5px;
    right: 12px;
    opacity: 0;
  
    &-item {
      cursor: pointer;
  
      &-btn {
        &:not(&:last-child) {
          margin-right: 12px;
        }
      }
    }
  }

  &:hover {
    .tool {
      opacity: 1;
    }
  }

  .loading {
    position: absolute;
    z-index: 100;
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    background-color: #f1e7e7;
    opacity: .5;
  }
  
  .header {
    width: calc(100% - 40px);
    height: 20px;
    cursor: move;
    user-select: none;
  }
  
  .content {
    position: relative;
    z-index: 2;

    .invalid {
      position: absolute;
      display: flex;
      overflow: auto;
      width: 100%;
      height: 100%;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;

      .title {
        font-size: 16px;
        font-weight: 700;
      }

      .desc {
        font-size: 12px;
        font-weight: 500;

        span {
          margin: 0 5px;
          color: #006ef9;
          cursor: pointer;
        }
      }
    }
  
    :global {
      .sdk-ai-bi-card {
        margin-top: 0 !important;
        box-shadow: none;
      }
    }
  }
  
  .change-input {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 20px;
  
    .close {
      cursor: pointer;
    }
  
    input {
      width: 100%;
    }
  }
}

