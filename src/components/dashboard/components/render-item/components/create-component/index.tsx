/* eslint-disable import/no-unresolved */
/* eslint-disable react/no-unstable-nested-components */
import React, { useState } from 'react';
import classNames from 'classnames';

import { Bubble, Icon } from '@tencent/tea-component';
import { DashboardItemTypeEnum } from '@src/contants';
import { changeCommonData, useCommonSelector } from '@src/store/app-common';
import { getUrlParam } from '@src/components/chat-bi/utils';
import { useDispatch } from 'react-redux';
import { addGraphToDashboardFinal, createDashboardId } from '@src/utils/get-data';
import cs from './index.module.scss';

interface CreateCompnentProps {
  width?: number;
  height?: number;
  apis: AppPluginAPI.PluginAPI;
}

export default function CreateComponent({
  width,
  height,
  apis,
}: CreateCompnentProps): React.ReactElement {
  const { dashBoardId, dashboardList } = useCommonSelector();
  const dispatch = useDispatch();
  const [addLoading, setAddLoading] = useState(false);
  const isConsole = apis?.env === 'CONSOLE';

  const setList = (list) => {
    dispatch(changeCommonData({
      dashboardList: list,
    }));
  };

  const handleAddText = () => {
    if (addLoading) return;
    const addItem = () => {
      setAddLoading(true);
      const item = {
        AppId: apis?.appId ?? Number(getUrlParam('appid')),
        ArchId: apis?.archInfo?.archId,
        Uin: apis.uin,
        DashBoardId: dashBoardId,
        GraphDesc: '',
        GraphName: '',
        DataText: 'text',
        Position: [0, 2, 6, 3],
        GraphType: DashboardItemTypeEnum.text,
        ChartOptions: JSON.stringify({}),
        DiagramId: '',
      };
      addGraphToDashboardFinal({ apiParams: item, isConsole }).then(({ GraphId }) => {
        setList([
          ...dashboardList,
          {
            ...item,
            GraphId: GraphId ?? `text-${new Date().getTime()}`,
          },
        ]);
      }).finally(() => {
        setAddLoading(false);
      });
    };

    if (!dashBoardId) {
      createDashboardId({
        isConsole,
        apiParams: {
          AppId: apis?.appId ?? Number(getUrlParam('appid')),
          ArchId: apis?.archInfo?.archId,
          Uin: apis.uin,
          DashBoardDesc: '',
          DashBoardName: '默认看板',
        },
      }).then((d) => {
        dispatch(changeCommonData({
          dashBoardId: d,
        }));
        addItem();
      });
      return;
    }
    addItem();
  };

  return (
    <div
      className={classNames(cs.text)}
      style={{
        width: `${width}px`,
        height: `${height}px`,
      }}
    >
      <Bubble content="添加文本" tooltip>
        <div style={{ zIndex: 2 }} className={cs.icon} onClick={handleAddText}>
          <Icon type={addLoading ? 'loading' : 'plus'} />
        </div>
      </Bubble>
    </div>
  );
}
