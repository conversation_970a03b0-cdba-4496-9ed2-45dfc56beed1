.text {
  display: flex;
  overflow: hidden;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &-title {
    position: relative;
    z-index: 2;
    display: block;
    width: 100%;
    cursor: text;
    font-size: 30px;
    text-align: center;

    > div[contenteditable='true']:focus {
      height: 40px;
      padding: 3px 5px;
      background-color: #fbfbfe;
      line-height: 40px;
      outline: none;
    }
  }

  &-desc {
    position: relative;
    z-index: 2;
    display: block;
    width: 100%;
    cursor: text;
    font-size: 14px;
    text-align: center;

    > div[contenteditable='true']:focus {
      height: 20px;
      padding: 3px 5px;
      background-color: #fbfbfe;
      line-height: 20px;
      outline: none;
    }
  }

  .tool {
    position: absolute;
    z-index: 2;
    top: 8px;
    right: 8px;
    opacity: 0;
  
    &-item {
      cursor: pointer;
  
      &-btn {
        &:not(&:last-child) {
          margin-right: 12px;
        }
      }
    }
  }

  &:hover {
    .tool {
      opacity: 1;
    }

    &::after {
      position: absolute;
      top: -1px;
      right: -1px;
      bottom: -1px;
      left: -1px;
      border: 1px solid #266fe8;
      border-radius: 4px;
      content: '';
      pointer-events: none;
    }
  }

  .drag {
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    cursor: move;
  }

  .icon {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
