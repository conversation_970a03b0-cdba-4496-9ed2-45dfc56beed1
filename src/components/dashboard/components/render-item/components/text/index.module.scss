.text {
  position: relative;
  display: flex;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &-title {
    position: relative;
    z-index: 2;
    display: block;
    overflow: auto;
    width: 100%;
    cursor: text;
    font-size: 30px;
    line-height: 30px;
    text-align: center;

    > div[contenteditable='true']:focus {
      height: 40px;
      padding: 3px 5px;
      background-color: #fbfbfe;
      line-height: 40px;
      outline: none;
    }
  }

  &-desc {
    position: relative;
    z-index: 2;
    display: block;
    overflow: auto;
    width: 100%;
    cursor: text;
    font-size: 14px;
    text-align: center;

    > div[contenteditable='true']:focus {
      height: 20px;
      padding: 3px 5px;
      background-color: #fbfbfe;
      line-height: 20px;
      outline: none;
    }
  }

  .tool {
    position: absolute;
    z-index: 2;
    top: 0px;
    right: 10px;
    opacity: 0;
  
    &-item {
      cursor: pointer;
  
      &-btn {
        &:not(&:last-child) {
          margin-right: 12px;
        }
      }
    }
  }

  &:hover {
    .tool {
      opacity: 1;
    }
  }

  .drag {
    position: absolute;
    z-index: 1;
    top: -10px;
    right: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    cursor: move;
  }
}
