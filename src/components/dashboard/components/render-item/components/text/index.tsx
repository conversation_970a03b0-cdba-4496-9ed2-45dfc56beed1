/* eslint-disable react/no-unstable-nested-components */
import React, { useRef } from 'react';
import classNames from 'classnames';
import {
  Bubble,
  Button,
  PopConfirm,
} from '@tencent/tea-component';
// eslint-disable-next-line import/no-extraneous-dependencies
import { DeleteIcon } from '@tencent/tea-icons-react';

import cs from './index.module.scss';

interface TextProps {
  title: string;
  type: string;
  width: number;
  height: number;
  isText?: boolean;
  onChange: (type: 'delete' | 'change', args?: { value: any }) => void;
}

const maxlength = 128;
// 是否在输入中文
let isComposing = false;
let textContent = '';
export default function Text({
  title,
  width,
  height,
  isText,
  onChange,
}: TextProps): React.ReactElement {
  const divRef = useRef(null);
  const handleChange = (event: any) => {
    const title = event.target.innerHTML;
    onChange('change', { value: title });
  };

  const handleChangeInput = () => {
    // 如果在输入中文，则暂不处理
    if (isComposing) {
      return;
    }
    sliceInnerText();
  };

  // 限制输入字数的方法
  const sliceInnerText = () => {
    const { innerText } = divRef.current;
    try {
      // 判断字数是否超出
      if (innerText.length > maxlength) {
        // 获取selection，这是一个很早就有的API
        const selection = window.getSelection();
        // 获取当前鼠标在输入框中的位置
        let { anchorOffset } = selection;
        // 判断已输入的文本是否超出最大长度
        if (textContent.length >= maxlength) {
          // 如果超出了文本长度，则修改innerText的内容为输入前的内容
          // 实际上就是限制了字符输入
          divRef.current.innerText = textContent;
          // 此时要将光标往前挪一位
          anchorOffset -= 1;
        }
        // 光标位置值不能超过最大长度，否则会报错
        // 如果不设置，在输入中文时，会报错
        if (anchorOffset > maxlength) {
          anchorOffset = maxlength;
        }
        // 将记录的光标位置重新设置一下
        selection.setPosition(divRef.current.childNodes[0], anchorOffset);
      }
    } catch (e) {
      console.error(e);
    }

    // 设置当前输入框的内容
    textContent = divRef.current?.innerText?.slice(0, maxlength) ?? '';
  };

  return (
    <div
      className={classNames(cs.text)}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        overflow: isText ? '' : 'hidden',
      }}
    >
      <span className={isText ? cs['text-desc'] : cs['text-title']}>
        <div
          ref={divRef}
          contentEditable
          onBlur={handleChange}
          onInput={handleChangeInput}
          dangerouslySetInnerHTML={{
            __html: title,
          }}
          onCompositionStart={() => {
            isComposing = true;
          }}
          onCompositionEnd={() => {
            isComposing = false;
            sliceInnerText();
          }}
        />
      </span>

      {
        isText ? (
          <div className={classNames('drag-handle', cs.drag)} />
        ) : null
      }

      {
        isText ? (
          <div className={cs.tool}>
            <div className={cs['tool-item']}>
              <PopConfirm
                title="确定删除该文本？"
                message="删除图表后无法恢复该文本"
                footer={(close) => (
                  <>
                    <Button
                      type="text"
                      onClick={() => {
                        close();
                        onChange('delete');
                      }}
                    >
                      确定
                    </Button>
                    <Button
                      type="text"
                      onClick={() => {
                        close();
                      }}
                    >
                      取消
                    </Button>
                  </>
                )}
                placement="top"
              >
                <Bubble content="删除文本" tooltip>
                  <DeleteIcon
                    color="#888888"
                    size={14}
                    className={cs['tool-item-btn']}
                  />
                </Bubble>
              </PopConfirm>
            </div>
          </div>
        ) : null
      }

    </div>
  );
}
