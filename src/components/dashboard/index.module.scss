$circle-size: 14px;
$offset: -($circle-size / 2);

.right {
  position: relative;
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;

  :global {
    .react-resizable-handle {
      z-index: 3;
      opacity: 0;
    }
  }

  .add {
    display: flex;
    align-items: center;
    margin-left: 15px;
    cursor: pointer;
  }

  .loading {
    position: absolute;
    z-index: 100;
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    background-color: #f1e7e7;
    opacity: .5;
  }

  .options {
    display: flex;
    height: 30px;
    align-items: center;
    justify-content: flex-end;

    input {
      top: 1px;
    }

    .icon {
      cursor: pointer;
    }
  }

  &-header {
    height: 40px;

    &-btn:not(:last-child) {
      margin-right: 10px;
    }
  }

  .layout {
    position: relative;
    overflow: auto;
    width: 100%;
    flex: 1;
    border-radius: 4px;
    background: #f2f4f8;
    
    .item {
      border-radius: 4px;
      background: #fff;

      &-autosizer {
        width: 100% !important;
        // height: 100% !important;
        height: 100px;
      }
    }
  }
}

.item {
  position: relative;
  width: 100%;
  height: 100%;
  height: 100px;
  box-sizing: border-box;
  padding: 10px;

  &:hover {
    &::after {
      position: absolute;
      top: -1px;
      right: -1px;
      bottom: -1px;
      left: -1px;
      border: 1px solid #266fe8;
      border-radius: 4px;
      content: '';
      pointer-events: none;
    }


    :global {
      .react-resizable-handle {
        z-index: 3;
        opacity: 1;
      }
    }
  }


  &-autosizer {
    width: 100% !important;
    height: 100% !important;
  }

  &:hover {
    .circle {
      opacity: 1;
    }
  }


  .circle {
    position: absolute;
    z-index: 4;
    display: flex;
    width: $circle-size;
    height: $circle-size;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #eeeaea;
    color: #006ef9;
    cursor: pointer;
    opacity: 0;
  }

  .tc {
    top: $offset; /* 半径 */
    left: 50%;
    transform: translateX(-50%);
  }

  /* 下边中心 */
  .bc {
    bottom: $offset;
    left: 50%;
    transform: translateX(-50%);
  }

  /* 左边中心 */
  .lc {
    top: 50%;
    left: $offset;
    transform: translateY(-50%);
  }

  /* 右边中心 */
  .rc {
    top: 50%;
    right: $offset;
    transform: translateY(-50%);
  }
}
