import React, { useEffect } from 'react';
import { Drawer } from 'tdesign-react';
import { EventEnum } from '@src/contants/event';
import { getPopupContainer } from '@src/utils/common-funcs';
import { useDispatch } from 'react-redux';
import { changeCommonData, useCommonSelector } from '@src/store/app-common';
import { v4 as uuidv4 } from 'uuid';
import { DRAWER_ID } from '@src/contants';
import {
  createDashboardId, getChartList,
  getDashboardDataFinal,
} from '@src/utils/get-data';
import { AddCircleIcon } from '@tencent/tea-icons-react';
import ChatBi from '../chat-bi';
import Dashboard from '../dashboard/index';
import cs from './index.module.scss';
import { getUrlParam } from '../chat-bi/utils';

interface DrawerContentProps {
  apis: AppPluginAPI.PluginAPI;
}
const DrawerContent = (props: DrawerContentProps) => {
  const { apis } = props;
  const dispatch = useDispatch();
  const {
    drawerVisible, boardVisible, dashBoardId, dashboardList,
  } = useCommonSelector();

  const isConsole = apis?.env === 'CONSOLE';

  useEffect(() => {
    apis?.pluginReportV2({
      event: 'visitDashboardOrChatbi',
      stringFields: {
        visitType: boardVisible ? 'dashboard' : 'chat',
      },
    });
  }, [boardVisible]);

  useEffect(() => {
    const openDrawer = () => {
      dispatch(changeCommonData({
        drawerVisible: true,
      }));
    };
    window.addEventListener(EventEnum.OPEN_DRAWER, openDrawer);

    return () => {
      window.removeEventListener(EventEnum.OPEN_DRAWER, openDrawer);
      dispatch(changeCommonData({
        selectedNode: '',
        drawerVisible: true,
        boardVisible: false,
        dashBoardId: '',
        sessionId: uuidv4(),
      }));
    };
  }, []);

  useEffect(() => {
    dispatch(changeCommonData({
      boardVisible: false,
    }));
    getChartList({
      isConsole,
      apiParams: {
        AppId: apis?.appId ?? Number(getUrlParam('appid')),
        ArchId: apis?.archInfo?.archId,
        Uin: apis?.uin,
      },
    }).then((res) => {
      if (res.DashBoardInfoList?.[0]?.DashBoardId) {
        dispatch(changeCommonData({
          dashBoardId: res?.DashBoardInfoList?.[0]?.DashBoardId,
        }));
      } else {
        // 没有id需要新建id
        createDashboard();
      }
    });
  }, []);

  useEffect(() => {
    if (dashBoardId) {
      getDashboardDataFinal({
        isConsole,
        apiParams: {
          AppId: apis?.appId ?? Number(getUrlParam('appid')),
          ArchId: apis?.archInfo?.archId,
          Uin: apis.uin,
          DashBoardId: dashBoardId,
        },
      }).then(({ GraphItems }) => {
        dispatch(changeCommonData({
          dashboardItemCount: GraphItems?.length ?? 0,
        }));
      });
    }
  }, [dashBoardId]);

  useEffect(() => {
    if (dashboardList) {
      dispatch(changeCommonData({
        dashboardItemCount: dashboardList?.length ?? 0,
      }));
    }
  }, [dashboardList]);

  const createDashboard = () => {
    createDashboardId({
      isConsole,
      apiParams: {
        AppId: apis?.appId ?? Number(getUrlParam('appid')),
        ArchId: apis?.archInfo?.archId,
        Uin: apis?.uin,
        DashBoardDesc: '',
        DashBoardName: '默认看板',
      },
    }).then((id) => {
      dispatch(changeCommonData({
        dashBoardId: id,
      }));
    });
  };

  const handleClose = () => {
    dispatch(changeCommonData({
      selectedNode: '',
      drawerVisible: false,
    }));
  };

  const toggleEditMode = () => {
    dispatch(changeCommonData({
      boardVisible: !boardVisible,
    }));
  };
  const getSize = () => {
    let preSize = localStorage.getItem('drawer-size');
    if (!preSize || preSize === 'NaN') {
      preSize = '500';
    }
    return `${preSize}px`;
  };

  const setSize = (size: number) => {
    localStorage.setItem('drawer-size', size.toString() === 'NaN' ? '500' : size.toString());
  };

  return (
    <Drawer
      visible={drawerVisible}
      onClose={handleClose}
      onSizeDragEnd={(s) => {
        setSize(s.size);
      }}
      sizeDraggable={{
        min: 500,
        max: 2000,
      }}
      className={DRAWER_ID}
      showOverlay={false}
      size={getSize()}
      footer={null}
      header={
        <div className={cs.header}>
          <div className={cs.new} onClick={() => window.dispatchEvent(new CustomEvent(EventEnum.NEW_CHAT))}>
            <AddCircleIcon />
            <span>新建对话</span>
          </div>
          <div className={cs.switch}>
            <div className={!boardVisible ? cs.active : cs.unactive} onClick={!boardVisible ? null : toggleEditMode}>
              ChatBI
            </div>
            <div
              id="dashboard-tab"
              className={!boardVisible ? cs.unactive : cs.active}
              onClick={!boardVisible ? toggleEditMode : null}
            >
              Dashboard
            </div>
          </div>
        </div>
      }
      attach={getPopupContainer}
      style={{
        zIndex: 500,
        userSelect: 'text',
      }}
    >
      <div className={cs.content} style={!boardVisible ? { display: 'none' } : {}}>
        { boardVisible ? <Dashboard apis={apis} /> : null }
      </div>
      <div className={cs.content} style={boardVisible ? { display: 'none' } : {}}>
        <ChatBi apis={apis} />
      </div>
    </Drawer>
  );
};

export default DrawerContent;
