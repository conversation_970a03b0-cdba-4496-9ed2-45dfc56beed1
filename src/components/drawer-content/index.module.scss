.container {
  display: flex;
  height: 100%;
  gap: 20px;

  .single-chart {
    overflow: hidden;
    width: 100%;
    height: 300px;
    padding: 0 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
}

.header {
  display: flex;
  width: calc(100% - 20px);
  justify-content: center;
 
  .switch {
    display: flex;
    height: 30px;
    box-sizing: border-box;
    flex: 1;
    flex-basis: 195px;
    flex-grow: 0;
    align-items: center;
    justify-content: center;
    padding: 4px 2px;
    border-radius: 4px;
    background-color: #f3f4f7;
  
    .switch-icon {
      margin-right: 6px;
    }
  
    .active {
      display: flex;
      height: 26px;
      flex: 1;
      align-items: center;
      justify-content: center;
      padding: 0 5px;
      border-radius: 4px;
      background-color: #006eff;
      color: #fff;
      cursor: pointer;
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
  
      .switch-icon {
        path {
          fill: #fff;
        }
      }
    }
  
    .unactive {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      padding: 0 5px;
      color: #7c878e;
      cursor: pointer;
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
  
      .switch-icon {
        path {
          fill: #7c878e;
        }
      }
    }
  }

  .new {
    position: absolute;
    top: 16px;
    left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 10px;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    background-color: #fff;
    color: #000;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    gap: 5px;

    &:hover {
      background-color: #f3ebeb;
    }
  }
}

.dashboard-title {
  display: flex;
  align-items: center;

  &-icon {
    margin-right: 10px;
    cursor: pointer;
  }
}

.content {
  overflow: auto;
  width: 100%;
  height: 100%;
  min-height: 100%;
}