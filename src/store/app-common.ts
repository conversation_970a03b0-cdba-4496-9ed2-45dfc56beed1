import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';

interface IAppCommonData {
  selectedNode?: string;
  drawerVisible?: boolean;
  boardVisible?: boolean;
  dashBoardId?: string;
  dashBoardName?: string;
  dashboardCheckId?: string;
  sessionId?: string;
  chatProducts?: string[];
  productsSchemaList?: {
    ProductName: string;
    TableDescribe: string;
    Columns: any[];
    SigmaId: string;
  }[];
  model?: string;
  autoRefresh?: boolean;
  dashboardList?: any[];
  changingGraphMap?: {[key: string]: any};
  dashboardItemCount?: number;
  isInsightMode?: boolean;
  defaultData?: AppPluginAPI.PluginAPI['defaultData'];
  chatVisible?: boolean; // chat对话组件是否显示
  chatOnBoardingData?: {
    title?: string;
    subTitle?: string;
    description?: string;
    quickList: {
      title?: string;
      description?: string;
    }[];
  },
  type?: 'drawer' | 'embedded',
  userName?: string;
}

const initialState: () => IAppCommonData = () => ({
  selectedNode: '',
  drawerVisible: true,
  boardVisible: false,
  dashBoardId: '',
  dashboardCheckId: '',
  sessionId: uuidv4(),
  chatProducts: [],
  productsSchemaList: [],
  model: 'channel1',
  autoRefresh: false,
  dashBoardName: '',
  dashboardList: [],
  changingGraphMap: {}, // 正在变化的图表
  dashboardItemCount: 0,
  isInsightMode: false,
  chatVisible: false,
  chatOnBoardingData: {
    subTitle: '我是云巡检Agent',
    description: '在这里我可以帮你分析左侧架构图的巡检风险情况与治理优先级',
    quickList: [],
  },
  type: 'drawer',
  userName: undefined,
});

export const appSlice = createSlice({
  name: 'appCommon',
  initialState: initialState(),
  reducers: {
    changeCommonData: (state, action: PayloadAction<IAppCommonData>) => {
      Object.assign(state, action.payload);
    },
    resetCommonData: (state) => {
      Object.assign(state, initialState());
    },
  },
});

export const { changeCommonData, resetCommonData } = appSlice.actions;

// 暴露出 appManager 切片数据的 selector
export const useCommonSelector: () => IAppCommonData = () => useSelector((state: any) => state.appCommon);

export default appSlice.reducer;
