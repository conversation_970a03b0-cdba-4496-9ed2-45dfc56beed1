/* eslint-disable max-len */
/* eslint-disable import/no-extraneous-dependencies */
/**
 * @fileoverview BI-SDK SDK 入口文件
 */
import React from 'react';
import { app } from '@tencent/tea-app';
import './i18n';
import { Provider } from 'react-redux';
import {
  initLLMMonitor,
} from '@qapm/sse-monitor';
import { EventEnum } from './contants/event';
// import icon from './assets/svgs/arch-level.svg';
import '@tencent/tea-component/dist/tea.css';
import './global.css';
import 'tdesign-react/es/style/index.css'; // 少量公共样式
import './tdesign-qcloud-style.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import '@tencent/cloud-chat-ui/dist/assets/style.css';
import './style.css';
import DrawerContent from './components/drawer-content';
import { store } from './store';
import ChatBI, { IotherBiProps } from './components/chat-bi';
import Dashboard from './components/dashboard';
import { getUrlParam } from './components/chat-bi/utils';
import { describeResourceProducts, getNodeInfo } from './utils/get-data';

const initSSEMonitor = (userName: string) => {
  const option = {
    app_key: '1d9ec168-17840',
    llm_ver: '',
    include_url_reg: [/sse/],

    use_fetch: true,
    use_xhr: false,
    use_event_source: false,
    user_id: userName,
    qapm_base_url: 'https://app.rumt-zh.com',
    version: '1.0.0',
    host_ip: window.location.host,
  };
  initLLMMonitor(option);
};

const initData = (apis) => {
  const isConsole = apis?.env === 'CONSOLE';
  describeResourceProducts({ apiParams: { Type: 'chatbi' }, isConsole }).then((d) => {
    if (d?.Products) {
      const products = d?.Products?.map((item) => item?.SigmaId);
      store.dispatch({
        type: 'appCommon/changeCommonData',
        payload: {
          chatProducts: products,
        },
      });
    }
  });
  const params = {
    AppId: apis?.appId ?? Number(getUrlParam('appid')),
    Action: 'DescribeArchChatSchemaList',
    ArchId: apis.archInfo.archId,
  };
  getNodeInfo({ apiParams: params, isConsole }).then((d) => {
    const data = d?.ArchChatSchemaList ?? [];
    store.dispatch({
      type: 'appCommon/changeCommonData',
      payload: {
        productsSchemaList: data,
      },
    });
  });
};

// 注册 SDK 入口，提供 SDK 工厂方法
app.sdk.register('ai-bi-for-inspection-sdk', () => ({
  init(props: AppPluginAPI.PluginAPI & IotherBiProps) {
    const {
      initOver, setSlotComponent, userName, useExportComponents, chatOnBoardingData,
    } = props ?? {};
    store.dispatch({
      type: 'appCommon/changeCommonData',
      payload: {
        userName,
      },
    });
    if (chatOnBoardingData) {
      store.dispatch({
        type: 'appCommon/changeCommonData',
        payload: {
          chatOnBoardingData,
        },
      });
    }
    initSSEMonitor(userName);
    initOver?.();
    if (!useExportComponents) {
      setSlotComponent?.(<Provider store={store}><DrawerContent apis={props} /></Provider>);
    }
    initData(props);
    // 返回编辑器事件处理函数
    return {
      onShapeClick: (param: { node: any, event: any }) => {
        const { node, event } = param;
        window.dispatchEvent(new CustomEvent(EventEnum.CLICK_NODE, { detail: { node, event } }));
      },
      onPluginClick: () => window.dispatchEvent(new CustomEvent(EventEnum.OPEN_DRAWER)),
      setSelectNode: (key) => {
        store.dispatch({
          type: 'appCommon/changeCommonData',
          payload: {
            selectedNode: key,
          },
        });
      },
      changeChatVisibleFlag: (flag) => {
        store.dispatch({
          type: 'appCommon/changeCommonData',
          payload: {
            chatVisible: flag,
          },
        });
      },
      component: {
        drawerContent: <Provider store={store}><DrawerContent apis={props} /></Provider>,
        biContent: <Provider store={store}><ChatBI apis={{ ...props, type: 'drawer' }} /></Provider>, // 抽屉类chatbi组件
        biEmbeddedContent: <Provider store={store}><ChatBI apis={{ ...props, type: 'embedded' }} /></Provider>, // 嵌入类chatbi组件
        dashboardContent: <Provider store={store}><Dashboard apis={props} /></Provider>,
      },
    };
  },
}));
