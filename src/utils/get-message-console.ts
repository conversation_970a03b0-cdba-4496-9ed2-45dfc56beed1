/* eslint-disable import/prefer-default-export */
/* eslint-disable max-len */
import {
  DescribeRedrawChartTaskIdParams,
  DescribeRedrawChartTaskIdResult,
  DescribeRedrawChartTaskResultParams,
  DescribeRedrawChartTaskResultResult,
  DescribeResourceProductsParam,
} from './get-message-console.type';
import { IDescribeResourceProductsResult } from './get-message.type';
import { ResultType, createServiceFunc } from './request';

/**
 * 获取产品列表
 * @url
 */
export const describeResourceProducts: (param: DescribeResourceProductsParam) => Promise<ResultType<IDescribeResourceProductsResult>> = createServiceFunc('DescribeResourceProducts', 'advisor', '2020-07-21');

/**
 * 获取产品指标
 * @url
 */
export const describeArchChatSchemaList: (params: {data: {ArchId: string}}) => Promise<ResultType<any>> = createServiceFunc('DescribeArchChatSchemaList', 'advisor', '2020-07-21');

/**
 * 通过sql获取数据
 * @url
 */
export const getChartDataConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('GetChartDataSet', 'advisor', '2020-07-21', { tipErr: false });

/**
 * 点赞点踩
 * @url
 */
export const createMessageFeedBackConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('CreateMessageFeedBack', 'advisor', '2020-07-21');

/**
 * 自然语言重新绘制图
 * @url
 */
export const redrawEchartConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('RedrawChart', 'advisor', '2020-07-21', { highCapacity: true } as any);

/**
 * 获取图表列表
 * @url
 */
export const describeDashboardListConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('DescribeDashboardList', 'advisor', '2020-07-21');

/**
 * 创建图表
 * @url
 */
export const createDashboardConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('CreateDashboard', 'advisor', '2020-07-21');

/**
 * 获取白板数据
 * @url
 */
export const getDashboardDataConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('GetDashboardData', 'advisor', '2020-07-21');

/**
 * 添加图表到白板
 * @url
 */
export const addGraphToDashboardConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('AddGraphToDashboard', 'advisor', '2020-07-21');

/**
 * 更新白板数据
 * @url
 */
export const updateDashBoardDataConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('UpdateDashBoardData', 'advisor', '2020-07-21', { highCapacity: true } as any);

/**
 * 删除白板数据
 * @url
 */
export const deleteDashboardGraphConsole: (params: any) => Promise<ResultType<any>> = createServiceFunc('DeleteDashboardGraph', 'advisor', '2020-07-21');

/**
 * 获取重新绘制echart图表任务ID
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRedrawChartTaskId
 * <AUTHOR>
 */
export const describeRedrawChartTaskId: (params: DescribeRedrawChartTaskIdParams) => Promise<ResultType<DescribeRedrawChartTaskIdResult>> = createServiceFunc('DescribeRedrawChartTaskId', 'advisor', '2020-07-21');

/**
 * 获取重新绘制echart图表任务结果
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRedrawChartTaskResult
 * <AUTHOR>
 */
export const describeRedrawChartTaskResult: (params: DescribeRedrawChartTaskResultParams) => Promise<ResultType<DescribeRedrawChartTaskResultResult>> = createServiceFunc('DescribeRedrawChartTaskResult', 'advisor', '2020-07-21');
