/* eslint-disable max-len */
import {
  IDescribeRiskManageSubjectListParams,
  IDescribeRiskManageSubjectListResponse,
  IDescribeSubAccountsByMainAccountResult,
  IDescribeSubscriptionEmailListV2Params,
  IDescribeSubscriptionEmailListV2Result,
  ICreateSubscriptionEmailV2Params,
  IUpdateSubscriptionEmailV2Params,
  IUpdateSubscriptionV2Params,
} from './get-risk-subject-console.type';
import { ResultType, createServiceFunc } from './request';
/**
 * @description 查询治理主题
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskManageSubjectList
 */
export const describeRiskManageSubjectList: (params: IDescribeRiskManageSubjectListParams) => Promise<ResultType<IDescribeRiskManageSubjectListResponse>> = createServiceFunc('DescribeRiskManageSubjectList', 'advisor', '2020-07-21');

/**
 * @description 查询主账号下的子账号列表
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeSubAccountsByMainAccount
 */
export const describeSubAccountsByMainAccount: () => Promise<ResultType<IDescribeSubAccountsByMainAccountResult>> = createServiceFunc('DescribeSubAccountsByMainAccount', 'advisor', '2020-07-21');

/**
 * @description 获取报告订阅邮箱列表
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeSubAccountsByMainAccount
 */
export const describeSubscriptionEmailListV2: (params: IDescribeSubscriptionEmailListV2Params) => Promise<ResultType<IDescribeSubscriptionEmailListV2Result>> = createServiceFunc('DescribeSubscriptionEmailListV2', 'advisor', '2020-07-21');

/**
 * @description 创建报告订阅邮箱
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */

export const createSubscriptionEmailV2: (params: ICreateSubscriptionEmailV2Params) => Promise<ResultType<unknown>> = createServiceFunc('CreateSubscriptionEmailV2', 'advisor', '2020-07-21');

/**
 * @description 更新报告订阅邮箱
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */

export const updateSubscriptionEmailV2: (params: IUpdateSubscriptionEmailV2Params) => Promise<ResultType<unknown>> = createServiceFunc('UpdateSubscriptionEmailV2', 'advisor', '2020-07-21');

/**
 * @description 创建或修改订阅信息V2
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateSubscriptionV2
 */

export const updateSubscriptionV2: (params: IUpdateSubscriptionV2Params) => Promise<ResultType<unknown>> = createServiceFunc('UpdateSubscriptionV2', 'advisor', '2020-07-21');
