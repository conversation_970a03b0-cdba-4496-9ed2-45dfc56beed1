import { message } from '@tencent/tea-component';

// 文字复制到剪切板
// 文字复制到剪切板
export const copyTextToClipboard = (text: string) => {
  // 使用 Clipboard API
  navigator.clipboard
    .writeText(text)
    .then(() => {
      message.success({ content: '复制成功' });
    })
    .catch(() => {
      message.error({ content: '复制失败' });
    });
};

// 获取微应用在基座应用里的挂载点
export const getPopupContainer = (): HTMLElement => document.querySelector(
  '#micro-frontend-root div[data-qiankun="isa-cloud-arch"]',
) || document.body;
