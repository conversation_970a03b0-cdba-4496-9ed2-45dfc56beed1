/* eslint-disable max-len */
import { post } from './request-admin';
import { ResponseResult } from './request.type';
import {
  IDescribeRiskManageSubjectListParams,
  IDescribeRiskManageSubjectListResponse,
  IDescribeSubAccountsByMainAccountParams,
  IDescribeSubscriptionEmailListV2Params,
  IDescribeSubscriptionEmailListV2Result,
  ICreateSubscriptionEmailV2Params,
  IUpdateSubscriptionEmailV2Params,
  IUpdateSubscriptionV2Params,
} from './get-risk-subject.type';
/**
 * 获取自定义主题
 * @url
 */
export const describeRiskManageSubjectList = (params: IDescribeRiskManageSubjectListParams): Promise<ResponseResult<IDescribeRiskManageSubjectListResponse>> => post('/1/interface', { ...params, Action: 'DescribeRiskManageSubjectList' });

/**
 * 获取产品列表
 * @url
 */
export const describeSubAccountsByMainAccount = (params: IDescribeSubAccountsByMainAccountParams): Promise<ResponseResult<IDescribeRiskManageSubjectListResponse>> => post('/1/interface', { ...params, Action: 'DescribeSubAccountsByMainAccount' });

/**
 * 获取订阅邮箱列表
 * @url
 */
export const describeSubscriptionEmailListV2 = (params: IDescribeSubscriptionEmailListV2Params): Promise<ResponseResult<IDescribeSubscriptionEmailListV2Result>> => post('/1/interface', { ...params, Action: 'DescribeSubscriptionEmailListV2' });

/**
 * 创建订阅邮箱
 * @url
 */
export const createSubscriptionEmailV2 = (params: ICreateSubscriptionEmailV2Params): Promise<ResponseResult<unknown>> => post('/1/interface', { ...params, Action: 'CreateSubscriptionEmailV2' });

/**
 * 更新订阅邮箱
 * @url
 */
export const updateSubscriptionEmailV2 = (params: IUpdateSubscriptionEmailV2Params): Promise<ResponseResult<unknown>> => post('/1/interface', { ...params, Action: 'UpdateSubscriptionEmailV2' });

/**
 * 创建或修改订阅信息V2
 * @url
 */
export const updateSubscriptionV2 = (params: IUpdateSubscriptionV2Params): Promise<ResponseResult<unknown>> => post('/1/interface', { ...params, Action: 'UpdateSubscriptionV2' });
