export interface DescribeResourceProductsParam {
  data: {
    Type: string;
  }
}

export interface IResourceProductInfoItem {
  ProductId: string;
  SigmaId: string;
  TsaProductId: string;
  ProductName: string;
  Category?: string;
}

export interface IDescribeResourceProductsResult {
  Products: IResourceProductInfoItem[];
}

export interface DescribeRedrawChartTaskIdParams {
  data: {
    Question: string;
    SessionID: string;
    ChartOptions: string;
    ArchId: string;
    NodeUUID: string;
  }
}

export interface DescribeRedrawChartTaskIdResult {
  TaskId: string;
}

export interface DescribeRedrawChartTaskResultParams {
  data: {
    TaskId: string;
    ArchId: string;
  }
}
export enum DescribeRedrawChartTaskResultStatus {
  init = 'init',
  running = 'running',
  finish = 'finished',
  failed = 'failed',
}
export interface DescribeRedrawChartTaskResultResult {
  Answer: string;
  Status: DescribeRedrawChartTaskResultStatus;
}
