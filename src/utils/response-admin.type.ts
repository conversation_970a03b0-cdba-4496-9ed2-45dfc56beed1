import { DashboardItemValid } from '@src/contants';

export interface CreateDashboardParams {
  AppId: number;
  ArchId: string;
  DashBoardDesc: string;
  DashBoardName: string;
  Uin: string;
}

export interface CreateDashboardResult {
  DashBoardId: string;
}

export interface DashBoardInfoItem {
  CreateTime: string;
  DashBoardDesc: string;
  DashBoardId: string;
  DashBoardName: string;
  UpdateTime: string;
}

export interface DescribeDashboardListParams {
  AppId: number;
  ArchId: string;
  Uin: string;
}

export interface DescribeDashboardListResult {
  DashBoardInfoList: DashBoardInfoItem[];
}

export interface GetDashboardDataParams {
  AppId: number;
  ArchId: string;
  Uin: string;
  DashBoardId: string;
}

export interface GraphItem {
  CreateTime: string;
  DataSetSQL: string;
  ChartOptions: string;
  GraphDesc: string;
  GraphId: string;
  GraphName: string;
  Position: number[];
  UpdateTime: string;
}

export interface GetDashboardDataResult {
  CreateTime: string;
  DashBoardDesc: string;
  DashBoardId: string;
  DashBoardName: string;
  GraphItems: GraphItem[];
  UpdateTime: string;
  DiagramId: string;
  NodeStatus: DashboardItemValid;
}

export interface AddGraphToDashboardParams {
  AppId: number;
  ArchId: string;
  DashBoardId: string;
  DataSetSQL?: string;
  ChartOptions?: string;
  GraphDesc: string;
  GraphName: string;
  Position: number[];
  Uin: string;
  DiagramId?: string;
}

export interface AddGraphToDashboardResult {
  GraphId: string;
}

export interface UpdatDashBoardDataParams {
  AppId: number;
  ArchId: string;
  DashBoardId: string;
  DashBoardName?: string;
  DashBoardDesc?: string;
  GraphItems: GraphItem[];
  Uin: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface UpdatDashBoardDataResult {}

export interface DeleteDashboardGraphParams {
  AppId: number;
  Uin: string ;
  ArchId: string ;
  DashBoardId: string ;
  GraphId: string[];
}
