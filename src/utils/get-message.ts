/* eslint-disable max-len */
import {
  CreateMessageFeedBackParams,
  DescribeArchChatSchemaListParams, DescribeArchChatSchemaListRes, DescribeRedrawChartTaskIdParams, DescribeRedrawChartTaskIdResult, DescribeRedrawChartTaskResultParams, DescribeRedrawChartTaskResultResult, GetChartDataParams, GetChartDataRes, IDescribeResourceProductsResult, RedrawEchartParams,
} from './get-message.type';
import { post } from './request-admin';
import { ResponseResult } from './request.type';

/**
 * 获取图表数据
 * @url
 */
export const getChartData = (params: GetChartDataParams): Promise<ResponseResult<GetChartDataRes>> => post('/1/interface', params);

/**
 * 重绘图表
 * @url
 */
export const redrawEchart = (params: RedrawEchartParams): Promise<ResponseResult<{Answer: string}>> => post('/1/interface', params);

/**
 * 获取schema指标列表
 * @url
 */
export const describeArchChatSchemaList = (params: DescribeArchChatSchemaListParams): Promise<ResponseResult<DescribeArchChatSchemaListRes>> => post('/1/interface', params);

/**
 * 创建反馈
 * @url
 */
export const createMessageFeedBack = (params: CreateMessageFeedBackParams): Promise<ResponseResult<{Answer: string}>> => post('/1/interface', params);

/**
 * 获取产品列表
 * @url
 */
export const describeResourceProducts = (params: { Type: string; }): Promise<ResponseResult<IDescribeResourceProductsResult>> => post('/1/interface', { ...params, Action: 'DescribeResourceProducts' });

/**
 * 获取重新绘制echart图表任务ID
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRedrawChartTaskId
 * <AUTHOR>
 */
export const describeRedrawChartTaskId = (params: DescribeRedrawChartTaskIdParams): Promise<ResponseResult<DescribeRedrawChartTaskIdResult>> => post('/1/interface', { ...params, Action: 'DescribeRedrawChartTaskId' });

/**
 * 获取重新绘制echart图表任务结果
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRedrawChartTaskResult
 * <AUTHOR>
 */
export const describeRedrawChartTaskResult = (params: DescribeRedrawChartTaskResultParams): Promise<ResponseResult<DescribeRedrawChartTaskResultResult>> => post('/1/interface', { ...params, Action: 'DescribeRedrawChartTaskResult' });
