export interface IDescribeRiskManageSubjectListParams {
  data: {
    ArchId: string;
    Limit: number;
    Offset: number;
  }
}

export interface IDescribeRiskManageSubjectListResponse {
  Total: number;
  Items: IScanRiskManageSubject[];
}

export interface IScanRiskManageSubject {
  /** 主题 ID (int64) */
  Id: number;
  /** 标题 */
  Title: string;
  /** 描述 */
  Description: string;
  /** 来源（1: Agent, 2: 自定义） */
  Source: number;
  /** 创建时间 (格式: YYYY-MM-DD HH:mm:ss) */
  CreateTime: string;
  /** 会话ID (用于查看Agent生成内容) */
  SessionId: string;
  /** 聊天ID (用于查看Agent生成内容) */
  ChatId: string;
  /** 作者 */
  Author: string;
}

export interface IDescribeSubAccountsByMainAccountResult {
  SubAccountInfoList: IDescribeSubAccountsByMainAccountItem[];
}

export interface IDescribeSubAccountsByMainAccountItem {
  Uin: number;
  Name: string;
  NickName: string;
  Uid: number;
}

export interface IDescribeSubscriptionEmailListV2Params {
  data: {
    SubId?: number;
    Email?: string;
    Limit: number;
    Offset: number;
    UserName?: string;
  }
}

export interface IDescribeSubscriptionEmailListV2Result {
  EmailList: string[];
  EmailDetails: IEmailItem[];
  TotalCount: number;
}

export interface IEmailItem {
  AppId: number;
  Email: string;
  SubUin: string;
  UpdateTime: string;
  UserName: string;
  Id: string;
  Status: OperateEnum; // 前端新增字段用于表格内渲染表单逻辑
  UserNameVerifyMsg?: string; // 前端新增业务字段，校验message
  EmailVerifyMsg?: string; // 前端新增业务字段，校验message
}

enum OperateEnum {
  add = 'add',
  edit = 'edit',
  view = 'view'
}

export interface ICreateSubscriptionEmailV2Params {
  data: {
    Email?: string;
    UserName?: string;
    SubUin: string;
  }
}

export interface IUpdateSubscriptionEmailV2Params {
  data: {
    Id: number;
    Email: string;
    UserName: string;
    SubUin: string;
  }
}

export interface IUpdateSubscriptionV2Params {
  data: {
    SubscriptionInfos: Array<SubscriptionInfo>;
  }
}

export interface SubscriptionInfo {
  Id: number;
  IsSubscribed: boolean;
  Name?: string;
  TplId?: number;
  TplName?: string;
  IsHideCusName?: boolean;
  TimeInfo: SubscriptionTimeInfo;
  EmailInfo: SubscriptionEmailInfo;
  EmailTitle: string;
  ReportType: number;
  ArchIds?: Array<string>;
  RiskLevel?: number;
  GroupIds?: Array<number>;
  Products?: string;
  Tags?: Array<{
    TagKey: string;
    TagValues: Array<string>;
  }>;
}

interface SubscriptionTimeInfo {
  Period: string;
  DayOfWeek?: number;
  Time: string;
  DaysOfWeek: string;
  Monthly?: string;
}

interface SubscriptionEmailInfo {
  EmailAddrs: Array<string>;
}
