/* eslint-disable max-len */
import { post } from './request-admin';
import { ResponseResult } from './request.type';
import {
  CreateDashboardParams,
  CreateDashboardResult,
  DescribeDashboardListParams,
  DescribeDashboardListResult,
  GetDashboardDataParams,
  GetDashboardDataResult,
  AddGraphToDashboardParams,
  AddGraphToDashboardResult,
  UpdatDashBoardDataParams,
  UpdatDashBoardDataResult,
  DeleteDashboardGraphParams,
} from './response-admin.type';

/**
 * 创建dashboard
 * <AUTHOR>
 * @wiki https://zhiyan.woa.com/plugin_ifbook/13534/repo/#/ifbook/repo/ifbrepo-4z0cB7s0JO/ver/baseline/interface/ifbif-FkNbktyHQ4/detail
 */
export const createDashboard: (
  params: CreateDashboardParams
) => Promise<ResponseResult<CreateDashboardResult>> = (params) => post('/1/interface', {
  Action: 'CreateDashboard',
  ...params,
});

/**
 * dashboard列表
 * <AUTHOR>
 * @wiki https://zhiyan.woa.com/plugin_ifbook/13534/repo/#/ifbook/repo/ifbrepo-4z0cB7s0JO/ver/baseline/interface/ifbif-tzl0NgWJaM/detail
 */
export const describeDashboardList: (
  params: DescribeDashboardListParams
) => Promise<ResponseResult<DescribeDashboardListResult>> = (params) => post('/1/interface', {
  Action: 'DescribeDashboardList',
  ...params,
});

/**
 * 获取dashboard
 * <AUTHOR>
 * @wiki https://zhiyan.woa.com/plugin_ifbook/13534/repo/#/ifbook/repo/ifbrepo-4z0cB7s0JO/ver/baseline/interface/ifbif-r7FGSpwXFg/detail
 */
export const getDashboardData: (
  params: GetDashboardDataParams
) => Promise<ResponseResult<GetDashboardDataResult>> = (params) => post('/1/interface', {
  Action: 'GetDashboardData',
  ...params,
});

/**
 * 添加图表到DashBoard
 * <AUTHOR>
 * @wiki https://zhiyan.woa.com/plugin_ifbook/13534/repo/#/ifbook/repo/ifbrepo-4z0cB7s0JO/ver/baseline/interface/ifbif-pstMR8kepO/detail
 */
export const addGraphToDashboard: (
  params: AddGraphToDashboardParams
) => Promise<ResponseResult<AddGraphToDashboardResult>> = (params) => post('/1/interface', {
  Action: 'AddGraphToDashboard',
  ...params,
});

/**
 * 添加图表到DashBoard
 * <AUTHOR>
 * @wiki https://zhiyan.woa.com/plugin_ifbook/13534/repo/#/ifbook/repo/ifbrepo-4z0cB7s0JO/ver/baseline/interface/ifbif-pstMR8kepO/detail
 */
export const UpdateDashBoardData: (
  params: UpdatDashBoardDataParams
) => Promise<ResponseResult<UpdatDashBoardDataResult>> = (params) => post('/1/interface', {
  Action: 'UpdateDashBoardData',
  ...params,
});

/**
 * 删除图表
 * <AUTHOR>
 */
export const deleteDashboardGraph: (
  params: DeleteDashboardGraphParams
) => Promise<ResponseResult<string>> = (params) => post('/1/interface', {
  Action: 'DeleteDashboardGraph',
  ...params,
});
