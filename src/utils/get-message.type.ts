import { DescribeRedrawChartTaskResultStatus } from './get-message-console.type';

export interface GetMessageReq {
  'content': string;
  'bot_app_key': string;
  'visitor_biz_id': string;
  'session_id': string;
  'visitor_labels': string[];
}

export interface GetChartDataParams {
  Action: string;
  DataSQL: string;
  SessionId: string;
  AppId: number;
  ArchId: string;
  NodeUUID: string;
  RequestId?: string;
}

export interface GetChartDataRes {
  DataSet: string;
  RequestId: string;
  Warning: string;
}

export interface RedrawEchartParams {
  Question: string;
  SessionID: string;
  Uin: string;
  Action: string;
  ApiSource: string;
  ChartOptions: string;
  ArchId: string;
  NodeUUID: string;
  AppId: number;
}

export interface DescribeArchChatSchemaListParams {
  AppId: number;
  Action: string;
  ArchId: string;
}

export interface DescribeArchChatSchemaListRes {
  ArchChatSchemaList: {
    ProductName: string;
    Columns: any[];
  }[];
}

export interface CreateMessageFeedBackParams {
  AppId: number;
  Action: string;
  ArchId: string;
  ChatId: string;
  FeedBack: number;
  SessionId: string;
  Uin: string;
  FeedBackMsg: string;
  IsAcceptReturnVisit: boolean;
}
interface IResourceProductInfoItem {
  ProductId: string;
  SigmaId: string;
  TsaProductId: string;
  ProductName: string;
}
export interface IDescribeResourceProductsResult {
  Products: IResourceProductInfoItem[];
}

export interface DescribeRedrawChartTaskIdParams {
  Question: string;
  SessionID: string;
  Uin: string;
  Action: string;
  ApiSource: string;
  ChartOptions: string;
  ArchId: string;
  NodeUUID: string;
  AppId: number;
}

export interface DescribeRedrawChartTaskIdResult {
  TaskId: string;
}

export interface DescribeRedrawChartTaskResultParams {
  TaskId: string;
  ArchId: string;
  AppId: number;
  Uin: string;
}

export interface DescribeRedrawChartTaskResultResult {
  Answer: string;
  Status: DescribeRedrawChartTaskResultStatus;
}
