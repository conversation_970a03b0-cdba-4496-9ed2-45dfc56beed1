/* eslint-disable no-promise-executor-return */
/* eslint-disable no-await-in-loop */
/* eslint-disable import/prefer-default-export */
import {
  describeResourceProducts as describeResourceProductsConsole,
  describeArchChatSchemaList as describeArchChatSchemaListConsole,
  getChartDataConsole,
  createMessageFeedBackConsole,
  describeDashboardListConsole,
  createDashboardConsole,
  getDashboardDataConsole,
  addGraphToDashboardConsole,
  updateDashBoardDataConsole,
  deleteDashboardGraphConsole,
  describeRedrawChartTaskId as describeRedrawChartTaskIdConsole,
  describeRedrawChartTaskResult as describeRedrawChartTaskResultConsole,
} from './get-message-console';
import {
  createMessageFeedBack,
  describeArchChatSchemaList, describeResourceProducts as describeResourceProductsAdmin, getChartData,
  describeRedrawChartTaskId, describeRedrawChartTaskResult,
} from './get-message';
import { DescribeRedrawChartTaskResultStatus, IDescribeResourceProductsResult } from './get-message-console.type';
import {
  addGraphToDashboard, createDashboard, deleteDashboardGraph, describeDashboardList, getDashboardData,
  UpdateDashBoardData,
} from './dashboard-admin';
import {
  AddGraphToDashboardResult, DescribeDashboardListResult, GetDashboardDataResult, UpdatDashBoardDataResult,
} from './response-admin.type';
import { DescribeArchChatSchemaListRes, GetChartDataRes } from './get-message.type';

interface DescribeResourceProducts {
  isConsole: boolean;
  apiParams: {
    Type: string;
  };
}
// 获取支持chat的产品列表
export const describeResourceProducts = async (params: DescribeResourceProducts):
Promise<IDescribeResourceProductsResult> => {
  const { isConsole, apiParams } = params;
  let res: IDescribeResourceProductsResult;
  if (isConsole) {
    const consoleRes = await describeResourceProductsConsole({
      data: apiParams,
    });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await describeResourceProductsAdmin(apiParams);
    res = adminRes?.Response;
  }
  return res;
};

interface CreateDashboardIdParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    ArchId: string;
    Uin: string;
    DashBoardDesc: string;
    DashBoardName: string;
  };
}
// 创建白板id,返回白板id
export const createDashboardId = async (params: CreateDashboardIdParams): Promise<string> => {
  const { isConsole, apiParams } = params;
  let res: string;
  if (isConsole) {
    const consoleRes = await createDashboardConsole({ data: { ArchId: apiParams.ArchId } });
    res = consoleRes?.data?.Response?.DashBoardId;
  } else {
    const adminRes = await createDashboard(apiParams);
    res = adminRes?.Response?.DashBoardId;
  }
  return res;
};

interface GetChartListParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    ArchId: string;
    Uin: string;
  }
}
// 获取白板图表列表
export const getChartList = async (params: GetChartListParams): Promise<DescribeDashboardListResult> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await describeDashboardListConsole({ data: { ArchId: apiParams.ArchId } });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await describeDashboardList(apiParams);
    res = adminRes?.Response;
  }
  return res;
};

interface GetNodeInfoParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    Action: string;
    ArchId: string;
  }
}
// 获取节点指标数据
export const getNodeInfo = async (params: GetNodeInfoParams): Promise<DescribeArchChatSchemaListRes> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await describeArchChatSchemaListConsole({ data: { ArchId: apiParams?.ArchId } });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await describeArchChatSchemaList(apiParams);
    res = adminRes?.Response;
  }
  return res;
};

interface GetEchartDataParams {
  isConsole: boolean;
  apiParams: {
    Action: string;
    DataSQL: string;
    SessionId: string;
    AppId: number;
    ArchId: string;
    NodeUUID: string;
    ChatId?: string;
  }
}
// sql获取数据
export const getEchartData = async (params: GetEchartDataParams): Promise<GetChartDataRes> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await getChartDataConsole({
      data: {
        DataSQL: apiParams?.DataSQL,
        SessionId: apiParams?.SessionId,
        ArchId: apiParams?.ArchId,
        NodeUUID: apiParams?.NodeUUID,
        ChatId: apiParams?.ChatId,
      },
    });
    res = consoleRes.code !== 0 ? (consoleRes?.data as any)?.data?.Response : consoleRes?.data?.Response;
  } else {
    const adminRes = await getChartData(apiParams);
    res = adminRes?.Response;
  }
  return res;
};

interface RedrawChartParams {
  isConsole: boolean;
  apiParams: {
    Question: string;
    SessionID: string;
    Uin: string;
    Action: string;
    ApiSource: string;
    ChartOptions: string;
    ArchId: string;
    NodeUUID: string;
    AppId: number;
  }
}
export const redrawChart = async (params: RedrawChartParams): Promise<{Answer: string}> => {
  const { isConsole, apiParams } = params;
  let res;

  // 先获取任务id
  if (isConsole) {
    const consoleRes = await describeRedrawChartTaskIdConsole({
      data: {
        Question: apiParams?.Question,
        SessionID: apiParams?.SessionID,
        ChartOptions: apiParams?.ChartOptions,
        ArchId: apiParams?.ArchId,
        NodeUUID: apiParams?.NodeUUID,
      },
    });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await describeRedrawChartTaskId(apiParams);
    res = adminRes?.Response;
  }

  if (!res?.TaskId) {
    return { Answer: '' }; // 或者抛出错误
  }

  // 通过任务id轮询获取数据
  const pollTask = async (taskId: string, archId: string | undefined) => {
    let result;
    do {
      if (isConsole) {
        const consoleRes = await describeRedrawChartTaskResultConsole({
          data: {
            TaskId: taskId,
            ArchId: archId,
          },
        });
        result = consoleRes?.data?.Response;
      } else {
        const adminRes = await describeRedrawChartTaskResult({
          TaskId: taskId,
          ArchId: archId,
          AppId: apiParams?.AppId,
          Uin: apiParams?.Uin,
        });
        result = adminRes?.Response;
      }
      if (result?.Status !== DescribeRedrawChartTaskResultStatus.finish
        && result?.Status !== DescribeRedrawChartTaskResultStatus.failed) {
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    } while (result?.Status !== DescribeRedrawChartTaskResultStatus.finish
      && result?.Status !== DescribeRedrawChartTaskResultStatus.failed
    );

    return result;
  };

  try {
    res = await pollTask(res.TaskId, apiParams?.ArchId);
    return res;
  } catch (error) {
    console.error('Error polling task:', error);
    throw error; // 或者返回错误信息
  }
};

interface CreateFeedBackParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    Action: string;
    ArchId: string;
    ChatId: string;
    FeedBack: number;
    SessionId: string;
    Uin: string;
    FeedBackMsg: string;
    IsAcceptReturnVisit: boolean;
  }
}
// 点赞点踩
export const createFeedBack = async (params: CreateFeedBackParams): Promise<{
  Answer: string;
}> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await createMessageFeedBackConsole({
      data: {
        ArchId: apiParams?.ArchId,
        ChatId: apiParams?.ChatId,
        FeedBack: apiParams?.FeedBack,
        SessionId: apiParams?.SessionId,
        FeedBackMsg: apiParams?.FeedBackMsg,
        IsAcceptReturnVisit: apiParams?.IsAcceptReturnVisit,
      },
    });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await createMessageFeedBack(apiParams);
    res = adminRes?.Response;
  }
  return res;
};

interface GetDashboardDataFinalParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    ArchId: string;
    Uin: string;
    DashBoardId: string;
  }
}
// 获取dashboard数据
export const getDashboardDataFinal = async (params: GetDashboardDataFinalParams): Promise<GetDashboardDataResult> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await getDashboardDataConsole({
      data: {
        ArchId: apiParams?.ArchId,
        DashBoardId: apiParams?.DashBoardId,
      },
    });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await getDashboardData(apiParams);
    res = adminRes?.Response;
  }
  return res;
};

interface AddGraphToDashboardFinalParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    ArchId: string;
    Uin: string;
    DashBoardId: string;
    DataSetSQL?: string;
    ChartOptions: string;
    GraphDesc: string;
    GraphName: string;
    Position: number[];
    GraphType: string;
    DiagramId: string;
    NodeName?: string;
    NodeType?: string;
    DataText?: string;
  }
}
// 添加图表到dashboard
export const addGraphToDashboardFinal = async (params: AddGraphToDashboardFinalParams): Promise<AddGraphToDashboardResult> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await addGraphToDashboardConsole({
      data: {
        ArchId: apiParams?.ArchId,
        DashBoardId: apiParams?.DashBoardId,
        DataSetSQL: apiParams?.DataSetSQL,
        ChartOptions: apiParams?.ChartOptions,
        GraphDesc: apiParams?.GraphDesc,
        GraphName: apiParams?.GraphName,
        Position: apiParams?.Position,
        GraphType: apiParams?.GraphType,
        DiagramId: apiParams?.DiagramId,
        NodeName: apiParams?.NodeName,
        NodeType: apiParams?.NodeType,
        DataText: apiParams?.DataText,
      },
    });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await addGraphToDashboard(apiParams);
    res = adminRes?.Response;
  }
  return res;
};

interface UpdateDashBoardDataFinalParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    ArchId: string;
    Uin: string;
    DashBoardId: string;
    GraphItems: {
      ChartOptions: string;
      DataText: string;
      GraphId: string;
      GraphType: string;
      NodeStatus: string;
      Position: number[];
      DataSetSQL: string;
      DiagramId: string;
      GraphName: string;
      NodeName: string;
      NodeType: string;
      GraphDesc: string;
    }[];
    DashBoardName: string;
  }
}
// 添加图表到dashboard
export const updateDashBoardDataFinal = async (params: UpdateDashBoardDataFinalParams): Promise<UpdatDashBoardDataResult> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await updateDashBoardDataConsole({
      data: {
        ArchId: apiParams?.ArchId,
        DashBoardId: apiParams?.DashBoardId,
        GraphItems: apiParams?.GraphItems?.map((item) => ({
          GraphId: item?.GraphId,
          GraphType: item?.GraphType,
          GraphName: item?.GraphName,
          GraphDesc: item?.GraphDesc,
          DataText: item?.DataText,
          DataSetSQL: item?.DataSetSQL,
          ChartOptions: item?.ChartOptions,
          Position: item?.Position,
        })),
        DashBoardName: apiParams?.DashBoardName,
      },
    });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await UpdateDashBoardData(apiParams as any);
    res = adminRes?.Response;
  }
  return res;
};

interface DeleteDashboardGraphFinalParams {
  isConsole: boolean;
  apiParams: {
    AppId: number;
    Uin: string;
    ArchId: string;
    DashBoardId: string;
    GraphId: string[];
  }
}
// 删除图表到
export const deleteDashboardGraphFinal = async (params: DeleteDashboardGraphFinalParams): Promise<string> => {
  const { isConsole, apiParams } = params;
  let res;
  if (isConsole) {
    const consoleRes = await deleteDashboardGraphConsole({
      data: {
        ArchId: apiParams?.ArchId,
        DashBoardId: apiParams?.DashBoardId,
        GraphId: apiParams?.GraphId,
      },
    });
    res = consoleRes?.data?.Response;
  } else {
    const adminRes = await deleteDashboardGraph(apiParams);
    res = adminRes?.Response;
  }
  return res;
};
