@keyframes shine-node {
  0% {
    fill: #006ef9; /* 蓝色 */
  }
  
  16.67% {
    fill: yellow; /* 黄色 */
  }
  
  33.33% {
    fill: #006ef9; /* 蓝色 */
  }
  
  50% {
    fill: yellow; /* 黄色 */
  }
  
  66.67% {
    fill: #006ef9; /* 蓝色 */
  }
  
  83.33% {
    fill: yellow; /* 黄色 */
  }
  
  100% {
    fill: yellow; /* 最终稳定为黄色 */
  }
}

.shine-node {
  .shape-body {
    .fill-dark {
      animation: shine-node 3s ease-in-out 1 forwards;
    }
  }
}

.color-node {
  .shape-body {
    .fill-dark {
      fill: yellow;
    }
  }
}

.chart-tooltip {
  overflow: auto;
  max-height: 250px;
}

.chat-ui-drawer__root {
  z-index: 1001 !important;
}