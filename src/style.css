
:root {
  --tea-font-size-1: var(--tea-font-size-300);
  --tea-font-size-2: var(--tea-font-size-350);
  --tea-font-size-3: var(--tea-font-size-400);
  --tea-font-size-4: var(--tea-font-size-450);
  --tea-font-size-5: var(--tea-font-size-500);
  --tea-font-size-6: var(--tea-font-size-600);
  --tea-font-size-7: var(--tea-font-size-700);
  --tea-font-size-8: var(--tea-font-size-800);
  --tea-font-size-9: var(--tea-font-size-900);
  --tea-font-size-10: var(--tea-font-size-1050);
  --tea-font-line-height-1: var(--tea-font-line-height-500);
  --tea-font-line-height-2: var(--tea-font-line-height-550);
  --tea-font-line-height-3: var(--tea-font-line-height-600);
  --tea-font-line-height-4: var(--tea-font-line-height-650);
  --tea-font-line-height-5: var(--tea-font-line-height-700);
  --tea-font-line-height-6: var(--tea-font-line-height-800);
  --tea-font-line-height-7: var(--tea-font-line-height-900);
  --tea-font-line-height-8: var(--tea-font-line-height-1000);
  --tea-font-line-height-9: var(--tea-font-line-height-1100);
  --tea-font-line-height-10: var(--tea-font-line-height-1350);
  --tea-font-family: var(--tea-font-family-default);
  --tea-typography-title-sm: var(--tea-typography-heading-8);
  --tea-typography-title-md: var(--tea-typography-heading-7);
  --tea-typography-title-lg: var(--tea-typography-heading-6);
  --tea-typography-title-xl: var(--tea-typography-heading-5);
  --tea-typography-headline-sm: var(--tea-typography-heading-4);
  --tea-typography-headline-md: var(--tea-typography-heading-3);
  --tea-typography-headline-lg: var(--tea-typography-heading-2);
  --tea-border-radius: var(--tea-border-radius-default);
  --tea-space-base: var(--tea-space-100);
  --tea-form-height: var(--tea-form-height-default);
  --tea-navbar-height: 50px;
  --tea-campaign-bar-height: 0px;
  --tea-safe-area-inset-vertical: calc(var(--tea-navbar-height) + var(--tea-campaign-bar-height));
}

:root:has(.qc-header-nav_layer_double) {
  --tea-navbar-height: 100px;
}

:root:has(.opc_resource) {
  --tea-campaign-bar-height: 51px;
}

.tea-theme-light,
:root,
[theme-mode=light] {
  color-scheme: light;
  --tea-color-text-link-default: var(--tea-color-palette-blue-8);
  --tea-color-text-link-hover: var(--tea-color-palette-blue-7);
  --tea-color-text-link-active: var(--tea-color-palette-blue-9);
  --tea-color-text-link-focus: var(--tea-color-palette-blue-5);
  --tea-color-text-link-disabled: var(--tea-color-palette-blue-3);
  --tea-color-text-solid: var(--tea-color-palette-black-100);
  --tea-color-text-primary: var(--tea-color-palette-black-90);
  --tea-color-text-secondary: var(--tea-color-palette-black-70);
  --tea-color-text-tertiary: var(--tea-color-palette-black-50);
  --tea-color-text-disabled: var(--tea-color-palette-black-30);
  --tea-color-text-paragraph: var(--tea-color-palette-black-90);
  --tea-color-text-brand-default: var(--tea-color-palette-blue-8);
  --tea-color-text-brand-hover: var(--tea-color-palette-blue-7);
  --tea-color-text-brand-active: var(--tea-color-palette-blue-9);
  --tea-color-text-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-text-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-text-warning-default: var(--tea-color-palette-orange-8);
  --tea-color-text-warning-hover: var(--tea-color-palette-orange-7);
  --tea-color-text-warning-active: var(--tea-color-palette-orange-9);
  --tea-color-text-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-text-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-text-success-default: var(--tea-color-palette-green-8);
  --tea-color-text-success-hover: var(--tea-color-palette-green-7);
  --tea-color-text-success-active: var(--tea-color-palette-green-9);
  --tea-color-text-success-focus: var(--tea-color-palette-green-5);
  --tea-color-text-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-text-error-default: var(--tea-color-palette-red-8);
  --tea-color-text-error-hover: var(--tea-color-palette-red-7);
  --tea-color-text-error-active: var(--tea-color-palette-red-9);
  --tea-color-text-error-focus: var(--tea-color-palette-red-5);
  --tea-color-text-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-text-on-bg-brand-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-brand-disabled: var(--tea-color-palette-white-70);
  --tea-color-text-on-bg-brand-lighten-default: var(--tea-color-palette-blue-9);
  --tea-color-text-on-bg-warning-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-warning-disabled: var(--tea-color-palette-white-70);
  --tea-color-text-on-bg-warning-lighten-default: var(--tea-color-palette-orange-9);
  --tea-color-text-on-bg-success-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-success-disabled: var(--tea-color-palette-white-70);
  --tea-color-text-on-bg-success-lighten-default: var(--tea-color-palette-green-9);
  --tea-color-text-on-bg-error-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-error-disabled: var(--tea-color-palette-white-70);
  --tea-color-text-on-bg-error-lighten-default: var(--tea-color-palette-red-9);
  --tea-color-text-on-bg-inverse-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-amber-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-yellow-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-amber-lighten-default: var(--tea-color-palette-amber-9);
  --tea-color-text-on-bg-yellow-lighten-default: var(--tea-color-palette-yellow-8);
  --tea-color-palette-white-0: hsla(0,0%,100%,0);
  --tea-color-palette-white-5: hsla(0,0%,100%,.05);
  --tea-color-palette-white-10: hsla(0,0%,100%,.1);
  --tea-color-palette-white-15: hsla(0,0%,100%,.15);
  --tea-color-palette-white-20: hsla(0,0%,100%,.2);
  --tea-color-palette-white-25: hsla(0,0%,100%,.25);
  --tea-color-palette-white-30: hsla(0,0%,100%,.3);
  --tea-color-palette-white-35: hsla(0,0%,100%,.35);
  --tea-color-palette-white-40: hsla(0,0%,100%,.4);
  --tea-color-palette-white-45: hsla(0,0%,100%,.45);
  --tea-color-palette-white-50: hsla(0,0%,100%,.5);
  --tea-color-palette-white-55: hsla(0,0%,100%,.55);
  --tea-color-palette-white-60: hsla(0,0%,100%,.6);
  --tea-color-palette-white-70: hsla(0,0%,100%,.7);
  --tea-color-palette-white-80: hsla(0,0%,100%,.8);
  --tea-color-palette-white-90: hsla(0,0%,100%,.9);
  --tea-color-palette-white-100: #fff;
  --tea-color-palette-black-0: transparent;
  --tea-color-palette-black-5: rgba(0,0,0,.05);
  --tea-color-palette-black-10: rgba(0,0,0,.1);
  --tea-color-palette-black-15: rgba(0,0,0,.15);
  --tea-color-palette-black-20: rgba(0,0,0,.2);
  --tea-color-palette-black-25: rgba(0,0,0,.25);
  --tea-color-palette-black-30: rgba(0,0,0,.3);
  --tea-color-palette-black-35: rgba(0,0,0,.35);
  --tea-color-palette-black-40: rgba(0,0,0,.4);
  --tea-color-palette-black-45: rgba(0,0,0,.45);
  --tea-color-palette-black-50: rgba(0,0,0,.5);
  --tea-color-palette-black-55: rgba(0,0,0,.55);
  --tea-color-palette-black-60: rgba(0,0,0,.6);
  --tea-color-palette-black-70: rgba(0,0,0,.7);
  --tea-color-palette-black-80: rgba(0,0,0,.8);
  --tea-color-palette-black-90: rgba(0,0,0,.9);
  --tea-color-palette-black-100: #000;
  --tea-color-palette-red-1: #fce8e8;
  --tea-color-palette-red-2: #f9d1d1;
  --tea-color-palette-red-3: #f5b9b9;
  --tea-color-palette-red-4: #f2a2a2;
  --tea-color-palette-red-5: #ef8b8b;
  --tea-color-palette-red-6: #ec7474;
  --tea-color-palette-red-7: #e85c5c;
  --tea-color-palette-red-8: #f64041;
  --tea-color-palette-red-9: #b42c3f;
  --tea-color-palette-red-10: #821238;
  --tea-color-palette-red-rgb: 246,64,65;
  --tea-color-palette-yellow-1: #fff6db;
  --tea-color-palette-yellow-2: #ffecbd;
  --tea-color-palette-yellow-3: #ffe7a4;
  --tea-color-palette-yellow-4: #ffde87;
  --tea-color-palette-yellow-5: #ffd364;
  --tea-color-palette-yellow-6: #ffcb47;
  --tea-color-palette-yellow-7: #fac235;
  --tea-color-palette-yellow-8: #e7ae1d;
  --tea-color-palette-yellow-9: #8f6f1e;
  --tea-color-palette-yellow-10: #624c15;
  --tea-color-palette-yellow-rgb: 231,174,29;
  --tea-color-palette-orange-1: #ffeddf;
  --tea-color-palette-orange-2: #ffdcbf;
  --tea-color-palette-orange-3: #ffca9f;
  --tea-color-palette-orange-4: #ffb980;
  --tea-color-palette-orange-5: #ffa760;
  --tea-color-palette-orange-6: #ff9540;
  --tea-color-palette-orange-7: #ff8420;
  --tea-color-palette-orange-8: #ff7800;
  --tea-color-palette-orange-9: #c04100;
  --tea-color-palette-orange-10: #800f00;
  --tea-color-palette-orange-rgb: 255,120,0;
  --tea-color-palette-amber-1: #fff3e4;
  --tea-color-palette-amber-2: #fce5ca;
  --tea-color-palette-amber-3: #fbdbb5;
  --tea-color-palette-amber-4: #f9ce9a;
  --tea-color-palette-amber-5: #f7c07e;
  --tea-color-palette-amber-6: #f5ae59;
  --tea-color-palette-amber-7: #f4a547;
  --tea-color-palette-amber-8: #f2972b;
  --tea-color-palette-amber-9: #c37c11;
  --tea-color-palette-amber-10: #5f401c;
  --tea-color-palette-amber-rgb: 242,151,43;
  --tea-color-palette-green-1: #e7f8f0;
  --tea-color-palette-green-2: #c2efd6;
  --tea-color-palette-green-3: #a3e7c2;
  --tea-color-palette-green-4: #85dfad;
  --tea-color-palette-green-5: #66d799;
  --tea-color-palette-green-6: #47cf84;
  --tea-color-palette-green-7: #29c770;
  --tea-color-palette-green-8: #0cbf5b;
  --tea-color-palette-green-9: #088f50;
  --tea-color-palette-green-10: #055e45;
  --tea-color-palette-green-rgb: 12,191,91;
  --tea-color-palette-blue-1: #e3ecff;
  --tea-color-palette-blue-2: #d4e3fc;
  --tea-color-palette-blue-3: #bbd3fb;
  --tea-color-palette-blue-4: #96bbf8;
  --tea-color-palette-blue-5: #699ef5;
  --tea-color-palette-blue-6: #4787f0;
  --tea-color-palette-blue-7: #266fe8;
  --tea-color-palette-blue-8: #0052d9;
  --tea-color-palette-blue-9: #0034b5;
  --tea-color-palette-blue-10: #001f97;
  --tea-color-palette-blue-rgb: 0,82,217;
  --tea-color-palette-gray-1: #f3f3f3;
  --tea-color-palette-gray-2: #eee;
  --tea-color-palette-gray-3: #e7e7e7;
  --tea-color-palette-gray-4: #dcdcdc;
  --tea-color-palette-gray-5: #c5c5c5;
  --tea-color-palette-gray-6: #a6a6a6;
  --tea-color-palette-gray-7: #8b8b8b;
  --tea-color-palette-gray-8: #777;
  --tea-color-palette-gray-9: #5e5e5e;
  --tea-color-palette-gray-10: #4b4b4b;
  --tea-color-palette-gray-11: #383838;
  --tea-color-palette-gray-12: #2c2c2c;
  --tea-color-palette-gray-13: #202020;
  --tea-color-palette-gray-14: #181818;
  --tea-color-palette-gray-15: #101010;
  --tea-color-palette-bluegray-0: #f7f8fb;
  --tea-color-palette-bluegray-1: #f2f4f8;
  --tea-color-palette-bluegray-2: #e9ecf1;
  --tea-color-palette-bluegray-3: #e6e9ef;
  --tea-color-palette-bluegray-4: #d6dbe3;
  --tea-color-palette-bluegray-5: #bcc4d0;
  --tea-color-palette-bluegray-6: #97a3b7;
  --tea-color-palette-bluegray-7: #7787a2;
  --tea-color-palette-bluegray-8: #5f7292;
  --tea-color-palette-bluegray-9: #4b5b76;
  --tea-color-palette-bluegray-10: #3c485c;
  --tea-color-palette-bluegray-11: #2c3645;
  --tea-color-palette-bluegray-12: #232a35;
  --tea-color-palette-bluegray-13: #1c222b;
  --tea-color-palette-bluegray-14: #13161b;
  --tea-color-border-brand-default: var(--tea-color-palette-blue-8);
  --tea-color-border-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-border-warning-default: var(--tea-color-palette-orange-8);
  --tea-color-border-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-border-success-default: var(--tea-color-palette-green-8);
  --tea-color-border-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-border-error-default: var(--tea-color-palette-red-8);
  --tea-color-border-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-border-primary-default: var(--tea-color-palette-bluegray-3);
  --tea-color-border-primary-hover: var(--tea-color-palette-blue-8);
  --tea-color-border-primary-focus: var(--tea-color-palette-blue-8);
  --tea-color-border-primary-active: var(--tea-color-palette-blue-8);
  --tea-color-border-primary-disabled: var(--tea-color-palette-bluegray-3);
  --tea-color-border-secondary-default: var(--tea-color-palette-bluegray-2);
  --tea-color-border-secondary-hover: var(--tea-color-palette-bluegray-3);
  --tea-color-border-secondary-focus: var(--tea-color-palette-blue-8);
  --tea-color-border-secondary-active: var(--tea-color-palette-blue-8);
  --tea-color-border-secondary-disabled: var(--tea-color-palette-bluegray-2);
  --tea-color-border-tertiary-default: var(--tea-color-palette-bluegray-4);
  --tea-color-border-tertiary-hover: var(--tea-color-palette-bluegray-5);
  --tea-color-border-tertiary-focus: var(--tea-color-palette-blue-8);
  --tea-color-border-tertiary-active: var(--tea-color-palette-blue-8);
  --tea-color-border-tertiary-disabled: var(--tea-color-palette-bluegray-4);
  --tea-color-border-on-bg-yellow-default: var(--tea-color-palette-yellow-7);
  --tea-color-border-on-bg-yellow-hover: #fff;
  --tea-color-border-on-bg-yellow-active: #fff;
  --tea-color-border-on-bg-yellow-focus: #fff;
  --tea-color-border-on-bg-yellow-disabled: #fff;
  --tea-color-border-on-bg-yellow-lighten-default: var(--tea-color-palette-yellow-7);
  --tea-color-border-on-bg-amber-default: var(--tea-color-palette-amber-7);
  --tea-color-border-on-bg-amber-hover: #fff;
  --tea-color-border-on-bg-amber-active: #fff;
  --tea-color-border-on-bg-amber-focus: #fff;
  --tea-color-border-on-bg-amber-color: #fff;
  --tea-color-border-on-bg-amber-lighten-default: var(--tea-color-palette-amber-7);
  --tea-color-border-on-bg-brand-default: var(--tea-color-palette-blue-8);
  --tea-color-border-on-bg-brand-hover: var(--tea-color-palette-blue-7);
  --tea-color-border-on-bg-brand-active: var(--tea-color-palette-blue-9);
  --tea-color-border-on-bg-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-border-on-bg-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-border-on-bg-brand-lighten-default: var(--tea-color-palette-blue-7);
  --tea-color-border-on-bg-warning-default: var(--tea-color-palette-orange-8);
  --tea-color-border-on-bg-warning-hover: var(--tea-color-palette-orange-7);
  --tea-color-border-on-bg-warning-active: var(--tea-color-palette-orange-9);
  --tea-color-border-on-bg-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-border-on-bg-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-border-on-bg-warning-lighten-default: var(--tea-color-palette-orange-7);
  --tea-color-border-on-bg-success-default: var(--tea-color-palette-green-8);
  --tea-color-border-on-bg-success-hover: var(--tea-color-palette-green-7);
  --tea-color-border-on-bg-success-active: var(--tea-color-palette-green-9);
  --tea-color-border-on-bg-success-focus: var(--tea-color-palette-green-5);
  --tea-color-border-on-bg-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-border-on-bg-success-lighten-default: var(--tea-color-palette-green-8);
  --tea-color-border-on-bg-error-default: var(--tea-color-palette-red-8);
  --tea-color-border-on-bg-error-hover: var(--tea-color-palette-red-7);
  --tea-color-border-on-bg-error-active: var(--tea-color-palette-red-9);
  --tea-color-border-on-bg-error-focus: var(--tea-color-palette-red-5);
  --tea-color-border-on-bg-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-border-on-bg-error-lighten-default: var(--tea-color-palette-red-7);
  --tea-color-border-on-bg-inverse-default: var(--tea-color-palette-gray-13);
  --tea-color-bg-page-default: var(--tea-color-palette-bluegray-1);
  --tea-color-bg-inverse-default: var(--tea-color-palette-gray-13);
  --tea-color-bg-primary-default: var(--tea-color-palette-white-100);
  --tea-color-bg-primary-hover: var(--tea-color-palette-bluegray-0);
  --tea-color-bg-primary-active: var(--tea-color-palette-bluegray-1);
  --tea-color-bg-primary-focus: var(--tea-color-palette-bluegray-1);
  --tea-color-bg-primary-disabled: var(--tea-color-palette-bluegray-1);
  --tea-color-bg-primary-lighten: var(--tea-color-palette-bluegray-0);
  --tea-color-bg-secondary-default: var(--tea-color-palette-bluegray-1);
  --tea-color-bg-secondary-hover: var(--tea-color-palette-bluegray-2);
  --tea-color-bg-secondary-active: var(--tea-color-palette-bluegray-2);
  --tea-color-bg-secondary-focus: var(--tea-color-palette-bluegray-2);
  --tea-color-bg-secondary-disabled: var(--tea-color-palette-bluegray-1);
  --tea-color-bg-tertiary-default: var(--tea-color-palette-bluegray-2);
  --tea-color-bg-tertiary-hover: var(--tea-color-palette-bluegray-3);
  --tea-color-bg-tertiary-active: var(--tea-color-palette-bluegray-3);
  --tea-color-bg-tertiary-focus: var(--tea-color-palette-bluegray-3);
  --tea-color-bg-tertiary-disabled: var(--tea-color-palette-bluegray-2);
  --tea-color-bg-brand-default: var(--tea-color-palette-blue-8);
  --tea-color-bg-brand-hover: var(--tea-color-palette-blue-7);
  --tea-color-bg-brand-active: var(--tea-color-palette-blue-9);
  --tea-color-bg-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-bg-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-bg-brand-lighten-default: var(--tea-color-palette-blue-1);
  --tea-color-bg-warning-default: var(--tea-color-palette-orange-8);
  --tea-color-bg-warning-hover: var(--tea-color-palette-orange-7);
  --tea-color-bg-warning-active: var(--tea-color-palette-orange-9);
  --tea-color-bg-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-bg-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-bg-warning-lighten-default: var(--tea-color-palette-orange-1);
  --tea-color-bg-success-default: var(--tea-color-palette-green-8);
  --tea-color-bg-success-hover: var(--tea-color-palette-green-7);
  --tea-color-bg-success-active: var(--tea-color-palette-green-9);
  --tea-color-bg-success-focus: var(--tea-color-palette-green-5);
  --tea-color-bg-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-bg-success-lighten-default: var(--tea-color-palette-green-1);
  --tea-color-bg-error-default: var(--tea-color-palette-red-8);
  --tea-color-bg-error-hover: var(--tea-color-palette-red-7);
  --tea-color-bg-error-active: var(--tea-color-palette-red-9);
  --tea-color-bg-error-focus: var(--tea-color-palette-red-5);
  --tea-color-bg-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-bg-error-lighten-default: var(--tea-color-palette-red-1);
  --tea-color-bg-amber-default: var(--tea-color-palette-amber-7);
  --tea-color-bg-amber-hover: #fff;
  --tea-color-bg-amber-active: #fff;
  --tea-color-bg-amber-focus: #fff;
  --tea-color-bg-amber-disabled: #fff;
  --tea-color-bg-amber-lighten-lighten: var(--tea-color-palette-amber-1);
  --tea-color-bg-yellow-default: var(--tea-color-palette-yellow-7);
  --tea-color-bg-yellow-hover: #fff;
  --tea-color-bg-yellow-focus: #fff;
  --tea-color-bg-yellow-active: #fff;
  --tea-color-bg-yellow-disabled: #fff;
  --tea-color-bg-yellow-lighten-lighten: var(--tea-color-palette-yellow-1);
  --tea-color-bg-scheme-mode: light;
  --tea-color-mask-primary: var(--tea-color-palette-black-60);
  --tea-color-mask-secondary: var(--tea-color-palette-white-60);
  --tea-linear-gradient-fixed-1: var(--tea-color-palette-black-5);
  --tea-linear-gradient-fixed-2: var(--tea-color-palette-black-0);
  --tea-typography-display-sm-font-size: var(--tea-font-size-900);
  --tea-typography-display-sm-font-family: var(--tea-font-family-default);
  --tea-typography-display-sm-line-height: var(--tea-font-line-height-1100);
  --tea-typography-display-sm-font-weight: var(--tea-font-weight-medium);
  --tea-typography-display-md-font-size: var(--tea-font-size-1050);
  --tea-typography-display-md-font-family: var(--tea-font-family-default);
  --tea-typography-display-md-font-weight: var(--tea-font-weight-medium);
  --tea-typography-display-md-line-height: var(--tea-font-line-height-1350);
  --tea-typography-heading-1-font-size: var(--tea-font-size-800);
  --tea-typography-heading-1-font-family: var(--tea-font-family-default);
  --tea-typography-heading-1-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-1-line-height: var(--tea-font-line-height-1000);
  --tea-typography-heading-2-font-size: var(--tea-font-size-700);
  --tea-typography-heading-2-font-family: var(--tea-font-family-default);
  --tea-typography-heading-2-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-2-line-height: var(--tea-font-line-height-900);
  --tea-typography-heading-3-font-size: var(--tea-font-size-600);
  --tea-typography-heading-3-font-family: var(--tea-font-family-default);
  --tea-typography-heading-3-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-3-line-height: var(--tea-font-line-height-800);
  --tea-typography-heading-4-font-size: var(--tea-font-size-500);
  --tea-typography-heading-4-font-family: var(--tea-font-family-default);
  --tea-typography-heading-4-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-4-line-height: var(--tea-font-line-height-700);
  --tea-typography-heading-5-font-size: var(--tea-font-size-450);
  --tea-typography-heading-5-font-family: var(--tea-font-family-default);
  --tea-typography-heading-5-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-5-line-height: var(--tea-font-line-height-650);
  --tea-typography-heading-6-font-size: var(--tea-font-size-400);
  --tea-typography-heading-6-font-family: var(--tea-font-family-default);
  --tea-typography-heading-6-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-6-line-height: var(--tea-font-line-height-600);
  --tea-typography-heading-7-font-size: var(--tea-font-size-350);
  --tea-typography-heading-7-font-family: var(--tea-font-family-default);
  --tea-typography-heading-7-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-7-line-height: var(--tea-font-line-height-550);
  --tea-typography-heading-8-font-size: var(--tea-font-size-300);
  --tea-typography-heading-8-font-family: var(--tea-font-family-default);
  --tea-typography-heading-8-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-8-line-height: var(--tea-font-line-height-500);
  --tea-typography-body-sm-font-size: var(--tea-font-size-300);
  --tea-typography-body-sm-font-family: var(--tea-font-family-default);
  --tea-typography-body-sm-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-sm-line-height: var(--tea-font-line-height-500);
  --tea-typography-body-md-font-size: var(--tea-font-size-350);
  --tea-typography-body-md-font-family: var(--tea-font-family-default);
  --tea-typography-body-md-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-md-line-height: var(--tea-font-line-height-550);
  --tea-typography-body-lg-font-size: var(--tea-font-size-400);
  --tea-typography-body-lg-font-family: var(--tea-font-family-default);
  --tea-typography-body-lg-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-lg-line-height: var(--tea-font-line-height-600);
  --tea-typography-body-xl-font-size: var(--tea-font-size-450);
  --tea-typography-body-xl-font-family: var(--tea-font-family-default);
  --tea-typography-body-xl-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-xl-line-height: var(--tea-font-line-height-650);
  --tea-size-blur-0: 0px;
  --tea-size-blur-50: 2px;
  --tea-size-blur-100: 4px;
  --tea-size-blur-200: 8px;
  --tea-size-blur-300: 12px;
  --tea-size-blur-400: 16px;
  --tea-size-blur-500: 20px;
  --tea-size-blur-600: 24px;
  --tea-size-blur-700: 28px;
  --tea-size-blur-800: 32px;
  --tea-size-blur-900: 36px;
  --tea-size-blur-1000: 40px;
  --tea-size-blur-1100: 44px;
  --tea-size-blur-1200: 48px;
  --tea-size-blur-1300: 52px;
  --tea-size-blur-1400: 56px;
  --tea-size-blur-1500: 60px;
  --tea-size-blur-1600: 64px;
  --tea-size-spread-0: 0px;
  --tea-size-spread-25: 1px;
  --tea-size-spread-50: 2px;
  --tea-size-spread-100: 4px;
  --tea-size-spread-200: 8px;
  --tea-size-spread-300: 12px;
  --tea-size-spread-400: 16px;
  --tea-size-spread-500: 20px;
  --tea-size-spread-600: 24px;
  --tea-size-spread-700: 28px;
  --tea-size-spread-800: 32px;
  --tea-size-spread-900: 36px;
  --tea-size-spread-1000: 40px;
  --tea-size-spread-1100: 44px;
  --tea-size-spread-1200: 48px;
  --tea-size-spread-0400: -16px;
  --tea-size-spread-0300: -12px;
  --tea-size-spread-0200: -8px;
  --tea-size-spread-0100: -4px;
  --tea-size-spread-050: -2px;
  --tea-size-spread-025: -1px;
  --tea-size-offset-0: 0px;
  --tea-size-offset-25: 1px;
  --tea-size-offset-50: 2px;
  --tea-size-offset-100: 4px;
  --tea-size-offset-200: 8px;
  --tea-size-offset-300: 12px;
  --tea-size-offset-400: 16px;
  --tea-size-offset-500: 20px;
  --tea-size-offset-600: 24px;
  --tea-font-size-300: 12px;
  --tea-font-size-325: 13px;
  --tea-font-size-350: 14px;
  --tea-font-size-400: 16px;
  --tea-font-size-450: 18px;
  --tea-font-size-500: 20px;
  --tea-font-size-600: 24px;
  --tea-font-size-700: 28px;
  --tea-font-size-800: 32px;
  --tea-font-size-900: 36px;
  --tea-font-size-950: 38px;
  --tea-font-size-1000: 40px;
  --tea-font-size-1050: 42px;
  --tea-font-size-default: var(--tea-font-size-300);
  --tea-font-family-default: -apple-system,BlinkMacSystemFont,'pingfang SC','Hiragina Sans GB','Helvetica Neue',Helvetica,'microsoft yahei ui','microsoft yahei',simsun,arial,sans-serif;
  --tea-font-family-code: Consolas,Menlo,Monaco,Andale Mono,Ubuntu Mono,monospace;
  --tea-font-weight-regular: 400;
  --tea-font-weight-medium: 600;
  --tea-font-line-height-300: 12px;
  --tea-font-line-height-350: 14px;
  --tea-font-line-height-400: 16px;
  --tea-font-line-height-450: 18px;
  --tea-font-line-height-500: 20px;
  --tea-font-line-height-550: 22px;
  --tea-font-line-height-600: 24px;
  --tea-font-line-height-650: 26px;
  --tea-font-line-height-700: 28px;
  --tea-font-line-height-800: 32px;
  --tea-font-line-height-850: 34px;
  --tea-font-line-height-900: 36px;
  --tea-font-line-height-950: 38px;
  --tea-font-line-height-1000: 40px;
  --tea-font-line-height-1050: 42px;
  --tea-font-line-height-1100: 44px;
  --tea-font-line-height-1150: 46px;
  --tea-font-line-height-1200: 48px;
  --tea-font-line-height-1250: 50px;
  --tea-font-line-height-1300: 52px;
  --tea-font-line-height-1350: 54px;
  --tea-font-line-height-default: var(--tea-font-line-height-500);
  --tea-border-radius-0: 0px;
  --tea-border-radius-50: 2px;
  --tea-border-radius-100: 4px;
  --tea-border-radius-150: 6px;
  --tea-border-radius-200: 8px;
  --tea-border-radius-300: 12px;
  --tea-border-radius-400: 16px;
  --tea-border-radius-500: 20px;
  --tea-border-radius-750: 30px;
  --tea-border-radius-default: var(--tea-border-radius-0);
  --tea-border-radius-full: 9999px;
  --tea-border-width-0: 0px;
  --tea-border-width-25: 1px;
  --tea-border-width-50: 2px;
  --tea-border-width-100: 4px;
  --tea-border-width-default: var(--tea-border-width-25);
  --tea-shadow-xs-blur-1: var(--tea-size-blur-100);
  --tea-shadow-xs-color-1: var(--tea-color-palette-black-5);
  --tea-shadow-xs-offsetx-1: var(--tea-size-offset-0);
  --tea-shadow-xs-offsety-1: var(--tea-size-offset-25);
  --tea-shadow-xs-spread-1: var(--tea-size-spread-0);
  --tea-shadow-sm-blur-1: var(--tea-size-blur-100);
  --tea-shadow-sm-blur-2: var(--tea-size-blur-200);
  --tea-shadow-sm-color-1: var(--tea-color-palette-black-5);
  --tea-shadow-sm-color-2: var(--tea-color-palette-black-5);
  --tea-shadow-sm-offsetx-1: var(--tea-size-offset-0);
  --tea-shadow-sm-offsetx-2: var(--tea-size-offset-0);
  --tea-shadow-sm-offsety-1: var(--tea-size-offset-50);
  --tea-shadow-sm-offsety-2: var(--tea-size-offset-50);
  --tea-shadow-sm-spread-1: var(--tea-size-spread-50);
  --tea-shadow-sm-spread-2: var(--tea-size-spread-025);
  --tea-shadow-md-blur-1: var(--tea-size-blur-300);
  --tea-shadow-md-blur-2: var(--tea-size-blur-600);
  --tea-shadow-md-color-1: var(--tea-color-palette-black-5);
  --tea-shadow-md-color-2: var(--tea-color-palette-black-10);
  --tea-shadow-md-offsetx-1: var(--tea-size-offset-0);
  --tea-shadow-md-offsetx-2: var(--tea-size-offset-0);
  --tea-shadow-md-offsety-1: var(--tea-size-offset-200);
  --tea-shadow-md-offsety-2: var(--tea-size-offset-200);
  --tea-shadow-md-spread-1: var(--tea-size-spread-0200);
  --tea-shadow-md-spread-2: var(--tea-size-spread-0100);
  --tea-shadow-lg-blur-1: var(--tea-size-blur-1200);
  --tea-shadow-lg-color-1: var(--tea-color-palette-black-15);
  --tea-shadow-lg-offsetx-1: var(--tea-size-offset-0);
  --tea-shadow-lg-offsety-1: var(--tea-size-offset-600);
  --tea-shadow-lg-spread-1: var(--tea-size-spread-0300);
  --tea-shadow-xl-blur-1: var(--tea-size-blur-1200);
  --tea-shadow-xl-color-1: var(--tea-color-palette-black-10);
  --tea-shadow-xl-offsetx-1: var(--tea-size-offset-0);
  --tea-shadow-xl-offsety-1: var(--tea-size-offset-600);
  --tea-shadow-xl-spread-1: var(--tea-size-spread-0300);
  --tea-form-height-default: 30px;
  --tea-form-color-bg-primary-default: var(--tea-color-bg-primary-default);
  --tea-form-color-bg-primary-hover: var(--tea-color-bg-primary-hover);
  --tea-form-color-bg-primary-active: var(--tea-color-bg-primary-active);
  --tea-form-color-bg-primary-focus: var(--tea-color-bg-primary-focus);
  --tea-form-color-bg-primary-disabled: var(--tea-color-bg-primary-disabled);
  --tea-form-color-border-primary-default: var(--tea-color-border-primary-default);
  --tea-form-color-border-primary-hover: var(--tea-color-border-primary-hover);
  --tea-form-color-border-primary-focus: var(--tea-color-border-primary-focus);
  --tea-form-color-border-primary-active: var(--tea-color-border-primary-active);
  --tea-form-color-border-primary-disabled: var(--tea-color-border-primary-disabled);
  --tea-form-color-text-solid: var(--tea-color-palette-black-100);
  --tea-form-color-text-primary: var(--tea-color-palette-black-90);
  --tea-form-color-text-secondary: var(--tea-color-palette-black-70);
  --tea-form-color-text-tertiary: var(--tea-color-palette-black-50);
  --tea-form-color-text-disabled: var(--tea-color-palette-black-30);
  --tea-space-0: 0px;
  --tea-space-100: 4px;
  --tea-space-200: 8px;
  --tea-space-300: 12px;
  --tea-space-400: 16px;
  --tea-space-500: 20px;
  --tea-space-600: 24px;
  --tea-space-700: 28px;
  --tea-space-800: 32px;
  --tea-space-900: 36px;
  --tea-space-1000: 40px;
  --tea-space-1100: 44px;
  --tea-space-1200: 48px;
  --tea-space-1300: 52px;
  --tea-space-1400: 56px;
  --tea-space-1500: 60px;
  --tea-space-1600: 64px;
  --tea-shadow-xs: var(--tea-shadow-xs-offsetx-1) var(--tea-shadow-xs-offsety-1) var(--tea-shadow-xs-blur-1) var(--tea-shadow-xs-spread-1) var(--tea-shadow-xs-color-1);
  --tea-shadow-sm: var(--tea-shadow-sm-offsetx-2) var(--tea-shadow-sm-offsety-2) var(--tea-shadow-sm-blur-2) var(--tea-shadow-sm-spread-2) var(--tea-shadow-sm-color-2),var(--tea-shadow-sm-offsetx-1) var(--tea-shadow-sm-offsety-1) var(--tea-shadow-sm-blur-1) var(--tea-shadow-sm-spread-1) var(--tea-shadow-sm-color-1);
  --tea-shadow-md: var(--tea-shadow-md-offsetx-2) var(--tea-shadow-md-offsety-2) var(--tea-shadow-md-blur-2) var(--tea-shadow-md-spread-2) var(--tea-shadow-md-color-2),var(--tea-shadow-md-offsetx-1) var(--tea-shadow-md-offsety-1) var(--tea-shadow-md-blur-1) var(--tea-shadow-md-spread-1) var(--tea-shadow-md-color-1);
  --tea-shadow-lg: var(--tea-shadow-lg-offsetx-1) var(--tea-shadow-lg-offsety-1) var(--tea-shadow-lg-blur-1) var(--tea-shadow-lg-spread-1) var(--tea-shadow-lg-color-1);
  --tea-shadow-xl: var(--tea-shadow-xl-offsetx-1) var(--tea-shadow-xl-offsety-1) var(--tea-shadow-xl-blur-1) var(--tea-shadow-xl-spread-1) var(--tea-shadow-xl-color-1);
  --tea-shadow-fixed-left: linear-gradient(270deg,var(--tea-linear-gradient-fixed-1),var(--tea-linear-gradient-fixed-2));
  --tea-shadow-fixed-right: linear-gradient(270deg,var(--tea-linear-gradient-fixed-2),var(--tea-linear-gradient-fixed-1));
  --tea-shadow-fixed-bottom: linear-gradient(0deg,var(--tea-linear-gradient-fixed-2),var(--tea-linear-gradient-fixed-1));
  --tea-shadow-fixed-top: linear-gradient(0deg,var(--tea-linear-gradient-fixed-1),var(--tea-linear-gradient-fixed-2));
  --tea-typography-display-sm: normal var(--tea-typography-display-sm-font-weight) var(--tea-typography-display-sm-font-size)/var(--tea-typography-display-sm-line-height) var(--tea-typography-display-sm-font-family);
  --tea-typography-display-md: normal var(--tea-typography-display-md-font-weight) var(--tea-typography-display-md-font-size)/var(--tea-typography-display-md-line-height) var(--tea-typography-display-md-font-family);
  --tea-typography-heading-1: normal var(--tea-typography-heading-1-font-weight) var(--tea-typography-heading-1-font-size)/var(--tea-typography-heading-1-line-height) var(--tea-typography-heading-1-font-family);
  --tea-typography-heading-2: normal var(--tea-typography-heading-2-font-weight) var(--tea-typography-heading-2-font-size)/var(--tea-typography-heading-2-line-height) var(--tea-typography-heading-2-font-family);
  --tea-typography-heading-3: normal var(--tea-typography-heading-3-font-weight) var(--tea-typography-heading-3-font-size)/var(--tea-typography-heading-3-line-height) var(--tea-typography-heading-3-font-family);
  --tea-typography-heading-4: normal var(--tea-typography-heading-4-font-weight) var(--tea-typography-heading-4-font-size)/var(--tea-typography-heading-4-line-height) var(--tea-typography-heading-4-font-family);
  --tea-typography-heading-5: normal var(--tea-typography-heading-5-font-weight) var(--tea-typography-heading-5-font-size)/var(--tea-typography-heading-5-line-height) var(--tea-typography-heading-5-font-family);
  --tea-typography-heading-6: normal var(--tea-typography-heading-6-font-weight) var(--tea-typography-heading-6-font-size)/var(--tea-typography-heading-6-line-height) var(--tea-typography-heading-6-font-family);
  --tea-typography-heading-7: normal var(--tea-typography-heading-7-font-weight) var(--tea-typography-heading-7-font-size)/var(--tea-typography-heading-7-line-height) var(--tea-typography-heading-7-font-family);
  --tea-typography-heading-8: normal var(--tea-typography-heading-8-font-weight) var(--tea-typography-heading-8-font-size)/var(--tea-typography-heading-8-line-height) var(--tea-typography-heading-8-font-family);
  --tea-typography-body-sm: normal var(--tea-typography-body-sm-font-weight) var(--tea-typography-body-sm-font-size)/var(--tea-typography-body-sm-line-height) var(--tea-typography-body-sm-font-family);
  --tea-typography-body-md: normal var(--tea-typography-body-md-font-weight) var(--tea-typography-body-md-font-size)/var(--tea-typography-body-md-line-height) var(--tea-typography-body-md-font-family);
  --tea-typography-body-lg: normal var(--tea-typography-body-lg-font-weight) var(--tea-typography-body-lg-font-size)/var(--tea-typography-body-lg-line-height) var(--tea-typography-body-lg-font-family);
  --tea-typography-body-xl: normal var(--tea-typography-body-xl-font-weight) var(--tea-typography-body-xl-font-size)/var(--tea-typography-body-xl-line-height) var(--tea-typography-body-xl-font-family);
  --tea-typography-body-default: var(--tea-typography-body-sm);
  --tea-linear-gradient-fixed-right: var(--tea-shadow-fixed-left);
  --tea-linear-gradient-fixed-left: var(--tea-shadow-fixed-left);
  --tea-white: var(--tea-color-palette-white-100);
  --tea-black: var(--tea-color-palette-black-100);
  --tea-color-palette-white-1: var(--tea-color-palette-white-100);
  --tea-color-palette-white-2: var(--tea-color-palette-white-90);
  --tea-color-palette-white-3: var(--tea-color-palette-white-55);
  --tea-color-palette-white-4: var(--tea-color-palette-white-35);
  --tea-color-palette-black-1: var(--tea-color-palette-black-100);
  --tea-color-palette-black-2: var(--tea-color-palette-black-90);
  --tea-color-palette-black-3: var(--tea-color-palette-black-70);
  --tea-color-palette-black-4: var(--tea-color-palette-black-50);
  --tea-color-function-brand-default: var(--tea-color-palette-blue-8);
  --tea-color-function-brand-bright: var(--tea-color-palette-blue-2);
  --tea-color-function-brand-darkness: var(--tea-color-palette-blue-10);
  --tea-color-function-brand-hover: var(--tea-color-palette-blue-7);
  --tea-color-function-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-function-brand-active: var(--tea-color-palette-blue-9);
  --tea-color-function-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-function-warning-default: var(--tea-color-palette-orange-8);
  --tea-color-function-warning-bright: var(--tea-color-palette-orange-2);
  --tea-color-function-warning-darkness: var(--tea-color-palette-orange-10);
  --tea-color-function-warning-hover: var(--tea-color-palette-orange-7);
  --tea-color-function-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-function-warning-active: var(--tea-color-palette-orange-9);
  --tea-color-function-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-function-error-default: var(--tea-color-palette-red-8);
  --tea-color-function-error-bright: var(--tea-color-palette-red-2);
  --tea-color-function-error-darkness: var(--tea-color-palette-red-10);
  --tea-color-function-error-hover: var(--tea-color-palette-red-7);
  --tea-color-function-error-focus: var(--tea-color-palette-red-5);
  --tea-color-function-error-active: var(--tea-color-palette-red-9);
  --tea-color-function-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-function-success-default: var(--tea-color-palette-green-8);
  --tea-color-function-success-bright: var(--tea-color-palette-green-2);
  --tea-color-function-success-darkness: var(--tea-color-palette-green-10);
  --tea-color-function-success-hover: var(--tea-color-palette-green-7);
  --tea-color-function-success-focus: var(--tea-color-palette-green-5);
  --tea-color-function-success-active: var(--tea-color-palette-green-9);
  --tea-color-function-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-text-placeholder: var(--tea-color-text-tertiary);
  --tea-color-text-inverse: var(--tea-color-palette-white-90);
  --tea-color-text-brand: var(--tea-color-text-brand-default);
  --tea-color-text-success: var(--tea-color-text-success-default);
  --tea-color-text-error: var(--tea-color-text-error-default);
  --tea-color-text-warning: var(--tea-color-text-warning-default);
  --tea-color-text-highlight-solid: var(--tea-color-palette-black-100);
  --tea-color-text-highlight-primary: var(--tea-color-palette-black-90);
  --tea-color-text-highlight-secondary: var(--tea-color-palette-black-70);
  --tea-color-text-highlight-placeholder: var(--tea-color-palette-black-50);
  --tea-color-text-highlight-disabled: var(--tea-color-palette-black-30);
  --tea-color-text-highlight-inverse-solid: var(--tea-color-palette-white-100);
  --tea-color-text-highlight-inverse-primary: var(--tea-color-palette-white-90);
  --tea-color-text-highlight-inverse-secondary: var(--tea-color-palette-white-55);
  --tea-color-text-highlight-inverse-placeholder: var(--tea-color-palette-white-35);
  --tea-color-text-highlight-inverse-disabled: var(--tea-color-palette-white-20);
  --tea-color-border-primary: var(--tea-color-border-primary-default);
  --tea-color-border-secondary: var(--tea-color-border-secondary-default);
  --tea-color-border-hover: var(--tea-color-border-primary-hover);
  --tea-color-border-disabled: var(--tea-color-border-primary-disabled);
  --tea-color-border-active: var(--tea-color-border-primary-active);
  --tea-color-bg-container-default: var(--tea-color-bg-primary-default);
  --tea-color-bg-container-hover: var(--tea-color-bg-primary-hover);
  --tea-color-bg-container-active: var(--tea-color-bg-primary-active);
  --tea-color-bg-container-inverse-default: var(--tea-color-palette-gray-14);
  --tea-color-bg-secondarycontainer-default: var(--tea-color-bg-secondary-default);
  --tea-color-bg-secondarycontainer-hover: var(--tea-color-bg-secondary-hover);
  --tea-color-bg-secondarycontainer-active: var(--tea-color-bg-secondary-active);
  --tea-color-bg-mask-default: var(--tea-color-mask-primary);
  --tea-color-bg-mask-secondary: var(--tea-color-mask-secondary);
  --tea-color-scrollbar-default: var(--tea-color-palette-black-20);
  --tea-color-scrollbar-hover: var(--tea-color-palette-black-40);
  --tea-color-scrolltrack: transparent;
  --tea-shadow-default: var(--tea-shadow-sm);
  --tea-color-bg-page: var(--tea-color-bg-page-default);
  --tea-shadow-dropdown: var(--tea-shadow-sm);
  --tea-color-bg-form-default: var(--tea-form-color-bg-primary-default);
  --tea-color-bg-form-hover: var(--tea-form-color-bg-primary-hover);
  --tea-color-bg-form-focus: var(--tea-form-color-bg-primary-focus);
  --tea-color-bg-form-disabled: var(--tea-form-color-bg-primary-disabled);
  --tea-color-text-form-default: var(--tea-form-color-text-primary);
  --tea-color-text-form-label: var(--tea-form-color-text-tertiary);
  --tea-color-text-form-hover: var(--tea-form-color-text-primary);
  --tea-color-text-form-focus: var(--tea-form-color-text-primary);
  --tea-color-text-form-disabled: var(--tea-form-color-text-disabled);
  --tea-color-text-form-placeholder: var(--tea-form-color-text-tertiary);
  --tea-color-border-form-default: var(--tea-form-color-border-primary-default);
  --tea-color-border-form-hover: var(--tea-form-color-border-primary-hover);
  --tea-color-border-form-focus: var(--tea-form-color-border-primary-focus);
  --tea-color-border-form-disabled: var(--tea-form-color-border-primary-disabled);
  --icon-status-blank: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGZpbGw9IiNGRkYiIGQ9Ik0yMCA1NlYyNGg0MHYzMnoiLz48cGF0aCBmaWxsPSIjQzFDQ0REIiBkPSJNMjYgMzdoMjh2MkgyNnptMCA3aDIwdjJIMjZ6bS02LTIwaDQwdjdIMjB6Ii8+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNDggNDQpIj48Y2lyY2xlIGZpbGw9IiNGRkYiIGN4PSIxMCIgY3k9IjEwIiByPSI3Ii8+PHBhdGggZD0iTTEwIDJhOCA4IDAgMSAxIDAgMTYgOCA4IDAgMCAxIDAtMTZ6bTEgMTBIOXYyaDJ2LTJ6bTAtNkg5djVoMlY2eiIgZmlsbD0iIzAwNkVGRiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-blank-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjRUJFRkY1IiBkPSJNMTQgMzkuMlYxNi44aDI4djIyLjR6Ii8+PHBhdGggZmlsbD0iI0MxQ0NERCIgZD0iTTE4LjIgMjUuOWgxOS42djEuNEgxOC4yem0wIDQuOWgxNHYxLjRoLTE0em0tNC4yLTE0aDI4djQuOUgxNHoiLz48cGF0aCBkPSJNNDAuNiAzMi4yYTUuNiA1LjYgMCAxIDAgMCAxMS4yIDUuNiA1LjYgMCAwIDAgMC0xMS4yeiIgZmlsbD0iIzAwNkVGRiIvPjxwYXRoIGQ9Ik00MS4zIDM5LjJ2MS40aC0xLjR2LTEuNGgxLjR6bTAtNC4ydjMuNWgtMS40VjM1aDEuNHoiIGZpbGw9IiNGRkYiLz48L2c+PC9zdmc+);
  --icon-status-chart: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGZpbGw9IiNGRkYiIGQ9Ik0yMiA1N1Y0M2gxMnYxNHoiLz48cGF0aCBmaWxsPSIjQzFDQ0REIiBkPSJNMzQgNTdWMzNoMTJ2MjR6Ii8+PHBhdGggc3Ryb2tlPSIjQzFDQ0REIiBzdHJva2Utd2lkdGg9IjIiIGQ9Ik0xOCAzOC43NDdsMTItMTJoN0w0My43NDcgMjAiLz48cGF0aCBmaWxsPSIjRkZGIiBkPSJNNDYgNTdWMjFoMTJ2MzZ6Ii8+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNDggNDQpIj48Y2lyY2xlIGZpbGw9IiNGRkYiIGN4PSIxMCIgY3k9IjEwIiByPSI3Ii8+PHBhdGggZD0iTTEwIDJhOCA4IDAgMSAxIDAgMTYgOCA4IDAgMCAxIDAtMTZ6bTEgMTBIOXYyaDJ2LTJ6bTAtNkg5djVoMlY2eiIgZmlsbD0iIzAwNkVGRiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-chart-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjRUJFRkY1IiBkPSJNMTUuNCAzOS45di05LjhoOC40djkuOHoiLz48cGF0aCBmaWxsPSIjQzFDQ0REIiBkPSJNMjMuOCAzOS45VjIzLjFoOC40djE2Ljh6Ii8+PHBhdGggZmlsbD0iI0MxQ0NERCIgZmlsbC1ydWxlPSJub256ZXJvIiBkPSJNMzAuMTI4IDEzLjUwNWwuOTkuOTktNC45MjggNC45MjhoLTQuOWwtOC4xOTUgOC4xOTUtLjk5LS45OSA4LjYwNS04LjYwNWg0Ljl6Ii8+PHBhdGggZmlsbD0iI0VCRUZGNSIgZD0iTTMyLjIgMzkuOVYxNC43aDguNHYyNS4yeiIvPjxwYXRoIGQ9Ik00MC42IDMyLjJhNS42IDUuNiAwIDEgMCAwIDExLjIgNS42IDUuNiAwIDAgMCAwLTExLjJ6IiBmaWxsPSIjMDA2RUZGIi8+PHBhdGggZD0iTTQxLjMgMzkuMnYxLjRoLTEuNHYtMS40aDEuNHptMC00LjJ2My41aC0xLjRWMzVoMS40eiIgZmlsbD0iI0ZGRiIvPjwvZz48L3N2Zz4=);
  --icon-status-loading: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGZpbGw9IiNGRkYiIGQ9Ik00MCAzNi44NzdMNjAuOTk0IDQ5IDQwIDYxLjEyMiAxOS4wMDMgNDl6Ii8+PHBhdGggZmlsbD0iI0MxQ0NERCIgZD0iTTQwIDI3Ljg3N0w2MC45OTQgNDAgNDAgNTIuMTIxIDE5LjAwMyA0MHoiLz48cGF0aCBmaWxsPSIjRkZGIiBkPSJNNDAgMTguODc4TDYwLjk5NCAzMSA0MCA0My4xMjMgMTkuMDAzIDMxeiIvPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDQ4IDQ0KSI+PGNpcmNsZSBmaWxsPSIjRkZGIiBjeD0iMTAiIGN5PSIxMCIgcj0iNyIvPjxwYXRoIGQ9Ik0xMCAyYTggOCAwIDEgMSAwIDE2IDggOCAwIDAgMSAwLTE2em0xIDNIOXY1LjkxNGwzLjI5MyAzLjI5MyAxLjQxNC0xLjQxNEwxMSAxMC4wODVWNXoiIGZpbGw9IiMwMDZFRkYiLz48L2c+PC9nPjwvc3ZnPg==);
  --icon-status-loading-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjRUJFRkY1IiBkPSJNMjggMjUuODE0TDQyLjY5NiAzNC4zIDI4IDQyLjc4NiAxMy4zMDIgMzQuM3oiLz48cGF0aCBmaWxsPSIjQzFDQ0REIiBkPSJNMjggMTkuNTE0TDQyLjY5NiAyOCAyOCAzNi40ODYgMTMuMzAyIDI4eiIvPjxwYXRoIGZpbGw9IiNFQkVGRjUiIGQ9Ik0yOCAxMy4yMTRMNDIuNjk2IDIxLjcgMjggMzAuMTg2IDEzLjMwMiAyMS43eiIvPjxwYXRoIGQ9Ik00MC42IDMyLjJhNS42IDUuNiAwIDEgMSAwIDExLjIgNS42IDUuNiAwIDAgMSAwLTExLjJ6IiBmaWxsPSIjMDA2RUZGIi8+PHBhdGggZmlsbD0iI0ZGRiIgZD0iTTQxLjMgMzQuM2gtMS40djQuMTRsMi4zMDUgMi4zMDUuOTktLjk5TDQxLjMgMzcuODZ6Ii8+PC9nPjwvc3ZnPg==);
  --icon-status-network: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDE2IDE2KSI+PGNpcmNsZSBmaWxsPSIjQzFDQ0REIiBjeD0iMjQiIGN5PSIyNCIgcj0iMjIiLz48cGF0aCBkPSJNMjQgMmgtLjEwM2MyLjQ4NiAwIDQuODc1LjQxMiA3LjEwNCAxLjE3MkwzMSAxMmgtN3Y5aC04djZoMTRsNSA1aDZsLjAwMSA1LjgzOGMtMy45NDcgNC44NzMtOS45MzUgOC4wMjYtMTYuNjY0IDguMTU4bC0uMzM3LjAwM1YzNmgtNnYtNEwzLjEwMyAxNy4xMDJDNS45OTcgOC4zMzEgMTQuMjYgMiAyNCAyeiIgZmlsbD0iI0ZGRiIvPjwvZz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg0OCA0NCkiPjxjaXJjbGUgZmlsbD0iI0ZGRiIgY3g9IjEwIiBjeT0iMTAiIHI9IjciLz48cGF0aCBkPSJNMTAgMmE4IDggMCAxIDEgMCAxNiA4IDggMCAwIDEgMC0xNnptMSAxMEg5djJoMnYtMnptMC02SDl2NWgyVjZ6IiBmaWxsPSIjMDA2RUZGIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-network-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGZpbGw9IiNDMUNDREQiIGN4PSIyOCIgY3k9IjI4IiByPSIxNS40Ii8+PHBhdGggZD0iTTI4IDEyLjZoLS4wNzJjMS43NCAwIDMuNDEzLjI4OSA0Ljk3Mi44MnY2LjE4SDI4djYuM2gtNS42djQuMmg5LjhsMy41IDMuNWg0LjJsLjAwMSA0LjA4NmExNS4zNyAxNS4zNyAwIDAgMS0xMS42NjUgNS43MTFsLS4yMzYuMDAyVjM2LjRoLTQuMnYtMi44TDEzLjM3MiAyMy4xNzFDMTUuMzk4IDE3LjAzMSAyMS4xODIgMTIuNiAyOCAxMi42eiIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGQ9Ik00MC42IDMyLjJhNS42IDUuNiAwIDEgMCAwIDExLjIgNS42IDUuNiAwIDAgMCAwLTExLjJ6IiBmaWxsPSIjMDA2RUZGIi8+PHBhdGggZD0iTTQxLjMgMzkuMnYxLjRoLTEuNHYtMS40aDEuNHptMC00LjJ2My41aC0xLjRWMzVoMS40eiIgZmlsbD0iI0ZGRiIvPjwvZz48L3N2Zz4=);
  --icon-status-no-permission: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGZpbGw9IiNDMUNDREQiIGQ9Ik00MCAzNi44NzdsMjAuOTk5IDEyLjEyNXYtMTlMNDAgMTcuODc4IDE4Ljk5OSAzMC4wMDJ2MTl6Ii8+PHBhdGggZmlsbD0iI0ZGRiIgZD0iTTQwIDQyLjEyM2wyMC45OTktMTIuMTI1djIwTDQwIDYyLjEyMyAxOC45OTkgNDkuOTk4di0yMHoiLz48cGF0aCBkPSJNNTggNDVhNSA1IDAgMCAxIDUgNXYxaDJ2MTFINTFWNTFoMnYtMWE1IDUgMCAwIDEgNS01em0wIDJhMyAzIDAgMCAwLTIuOTk1IDIuODI0TDU1IDUwdjFoNnYtMWEzIDMgMCAwIDAtMi44MjQtMi45OTVMNTggNDd6IiBmaWxsPSIjMDA2RUZGIi8+PHBhdGggZmlsbD0iI0ZGRiIgZD0iTTU3IDU0aDJ2NWgtMnoiLz48L2c+PC9zdmc+);
  --icon-status-no-permission-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjQzFDQ0REIiBkPSJNMjggMjUuODE0bDE0LjcgOC40ODh2LTEzLjNMMjggMTIuNTE0bC0xNC43IDguNDg4djEzLjN6Ii8+PHBhdGggZmlsbD0iI0VCRUZGNSIgZD0iTTI4IDI5LjQ4NmwxNC43LTguNDg4djE0TDI4IDQzLjQ4NmwtMTQuNy04LjQ4OHYtMTR6Ii8+PHBhdGggZD0iTTQwLjYgMzEuNWEzLjUgMy41IDAgMCAxIDMuNSAzLjV2LjY5OWwxLjQuMDAxdjcuN2gtOS44di03LjdsMS40LS4wMDFWMzVhMy41IDMuNSAwIDAgMSAzLjUtMy41em0wIDEuNGEyLjEgMi4xIDAgMCAwLTIuMDk2IDEuOTc3TDM4LjUgMzV2LjdoNC4yVjM1YTIuMSAyLjEgMCAwIDAtMS45NzctMi4wOTZMNDAuNiAzMi45eiIgZmlsbD0iIzAwNkVGRiIvPjxwYXRoIGZpbGw9IiNGRkYiIGQ9Ik0zOS45IDM3LjhoMS40djMuNWgtMS40eiIvPjwvZz48L3N2Zz4=);
  --icon-status-pay: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGZpbGw9IiNGRkYiIGQ9Ik0xOSA1NVYyNWg0MnYzMHoiLz48cGF0aCBmaWxsPSIjQzFDQ0REIiBkPSJNNDggNDJoOXY5aC05ek0xOSAzMGg0MnY4SDE5eiIvPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDQ4IDQ0KSI+PGNpcmNsZSBmaWxsPSIjRkZGIiBjeD0iMTAiIGN5PSIxMCIgcj0iNyIvPjxwYXRoIGQ9Ik0xMCAyYTggOCAwIDEgMSAwIDE2IDggOCAwIDAgMSAwLTE2em0xIDEwSDl2Mmgydi0yem0wLTZIOXY1aDJWNnoiIGZpbGw9IiMwMDZFRkYiLz48L2c+PC9nPjwvc3ZnPg==);
  --icon-status-pay-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjRUJFRkY1IiBkPSJNMTMuMyAzOC41di0yMWgyOS40djIxeiIvPjxwYXRoIGZpbGw9IiNDMUNDREQiIGQ9Ik0zMy42IDI5LjRoNi4zdjYuM2gtNi4zek0xMy4zIDIxaDI5LjR2NS42SDEzLjN6Ii8+PHBhdGggZD0iTTQwLjYgMzIuMmE1LjYgNS42IDAgMSAwIDAgMTEuMiA1LjYgNS42IDAgMCAwIDAtMTEuMnoiIGZpbGw9IiMwMDZFRkYiLz48cGF0aCBkPSJNNDEuMyAzOS4ydjEuNGgtMS40di0xLjRoMS40em0wLTQuMnYzLjVoLTEuNFYzNWgxLjR6IiBmaWxsPSIjRkZGIi8+PC9nPjwvc3ZnPg==);
  --icon-status-search: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGZpbGw9IiNDMUNDREQiIGQ9Ik0xOSAyM2gxNWw0IDRoMTl2MzBIMTl6Ii8+PHBhdGggZD0iTTYzLjQzMSAzM0w1NyA1N0gxOWw2LjQzMS0yNGgzOHoiIGZpbGw9IiNGRkYiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg0OCA0NCkiPjxjaXJjbGUgZmlsbD0iI0ZGRiIgY3g9IjEwIiBjeT0iMTAiIHI9IjciLz48cGF0aCBkPSJNMTAgMmE4IDggMCAxIDAgMCAxNiA4IDggMCAwIDAgMC0xNnoiIGZpbGw9IiMwMDZFRkYiLz48cGF0aCBkPSJNOS4yNTIgNWE0LjI1MiA0LjI1MiAwIDAgMSAzLjYzIDYuNDY3bDEuNzc1IDEuNzc2LTEuNDE0IDEuNDE0LTEuNzc2LTEuNzc2QTQuMjUyIDQuMjUyIDAgMSAxIDkuMjUxIDV6bTAgMmEyLjI1MiAyLjI1MiAwIDEgMCAwIDQuNTA0IDIuMjUyIDIuMjUyIDAgMCAwIDAtNC41MDR6IiBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L2c+PC9nPjwvc3ZnPg==);
  --icon-status-search-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjQzFDQ0REIiBkPSJNMTMuMyAxNi4xaDEwLjVsMi44IDIuOGgxMy4zdjIxSDEzLjN6Ii8+PHBhdGggZD0iTTQ0LjQwMiAyMy4xTDM5LjkgMzkuOUgxMy4zbDQuNTAyLTE2LjhoMjYuNnoiIGZpbGw9IiNFQkVGRjUiLz48cGF0aCBkPSJNNDAuNiAzMi4yYTUuNiA1LjYgMCAxIDAgMCAxMS4yIDUuNiA1LjYgMCAwIDAgMC0xMS4yeiIgZmlsbD0iIzAwNkVGRiIvPjxwYXRoIGQ9Ik00MC4wNzYgMzQuM2EyLjk3NiAyLjk3NiAwIDAgMSAyLjU0MSA0LjUyN2wxLjI0MyAxLjI0My0uOTkuOTktMS4yNDMtMS4yNDNhMi45NzYgMi45NzYgMCAxIDEtMS41NS01LjUxN3ptMCAxLjRhMS41NzYgMS41NzYgMCAxIDAgMCAzLjE1MyAxLjU3NiAxLjU3NiAwIDAgMCAwLTMuMTUzeiIgZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9nPjwvc3ZnPg==);
  --icon-status-upload: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iI0VCRUZGNSIvPjxwYXRoIGZpbGw9IiNGRkYiIGQ9Ik0yMCA1NlYyNGg0MHYzMnoiLz48cGF0aCBkPSJNMzEgMzIuMTNsMTggMTggMS40MTQtMS40MTQtNS01TDUxIDM4LjEzbDkgOVY1NkgyMFY0My4xMjlsMTEtMTF6IiBmaWxsPSIjQzFDQ0REIi8+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNDggNDQpIj48Y2lyY2xlIGZpbGw9IiNGRkYiIGN4PSIxMCIgY3k9IjEwIiByPSI3Ii8+PHBhdGggZD0iTTE0IDExaC0zdjNIOXYtM0g2VjloM1Y2aDJ2M2gzdjJ6TTIgMTBhOCA4IDAgMSAwIDE2IDAgOCA4IDAgMCAwLTE2IDB6IiBmaWxsPSIjMDA2RUZGIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-upload-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjRUJFRkY1IiBkPSJNMTQgMzkuMlYxNi44aDI4djIyLjR6Ii8+PHBhdGggZD0iTTIxLjcgMjIuNDkxbDEyLjYgMTIuNi45OS0uOTktMy41LTMuNDk5IDMuOTEtMy45MTEgNi4zIDYuMzAxVjM5LjJIMTR2LTkuMDFsNy43LTcuNjk5eiIgZmlsbD0iI0MxQ0NERCIvPjxwYXRoIGQ9Ik0zNSAzNy44YTUuNiA1LjYgMCAxIDAgMTEuMiAwIDUuNiA1LjYgMCAwIDAtMTEuMiAweiIgZmlsbD0iIzAwNkVGRiIvPjxwYXRoIGZpbGw9IiNGRkYiIGQ9Ik00My40IDM4LjVoLTIuMXYyLjFoLTEuNHYtMi4xaC0yLjF2LTEuNGgyLjFWMzVoMS40djIuMWgyLjF6Ii8+PC9nPjwvc3ZnPg==);
  --icon-status-blank-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zOC45OSAyMy45MzJhMiAyIDAgMCAxIDIuNDUtMS40MTVsMTguODk5IDUuMDY0YTIgMiAwIDAgMSAxLjQxNiAyLjQ0TDU2LjUgNTAuMDI2IDQ3Ljk5OSA1NGwtMTQuMDI0LTMuNDg0YTIgMiAwIDAgMS0xLjQ1LTIuNDU4bDYuNDY1LTI0LjEyNnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjgzMjkgLjg0NjUgLjg4NzEpIi8+PHBhdGggZD0iTTQ5LjM4MiA0OS44NTRMNDggNTRsOC41LTQtNC42MzItMS40MjVhMiAyIDAgMCAwLTIuNDg2IDEuMjc5eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuNzM3NCAuNzU4NyAuODIyNikiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTIuMDc4IDE5LjA5NmEyIDIgMCAwIDEgMS40MTQtMi40NWwyNy4zMTgtNy4zMmEyIDIgMCAwIDEgMi40NSAxLjQxNGw5LjAwNSAzMy42MDhhMiAyIDAgMCAxLTEuNDE0IDIuNDVsLTI3LjMxOCA3LjMyYTIgMiAwIDAgMS0yLjQ1LTEuNDE1TDIuMDc4IDE5LjA5NnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzggLjkxOTIgLjk0NjIpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05LjgzIDI0Ljk2M2wxOS4zMTgtNS4xNzYgMS4wMzYgMy44NjQtMTkuMzE5IDUuMTc2LTEuMDM1LTMuODY0em0xLjU1MyA1Ljc5NmwxMC40NDEtMi43OTggMS4wMzUgMy44NjQtMTAuNDQxIDIuNzk4LTEuMDM1LTMuODY0eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODA4OSAuODI2NyAuODcxMSkiLz48cGF0aCBkPSJNNTggMTAuNWwuMzg3IDEuMDQ1YTMuNSAzLjUgMCAwIDAgMi4wNjggMi4wNjhMNjEuNSAxNGwtMS4wNDUuMzg3YTMuNSAzLjUgMCAwIDAtMi4wNjggMi4wNjhMNTggMTcuNWwtLjM4Ny0xLjA0NWEzLjUgMy41IDAgMCAwLTIuMDY4LTIuMDY4TDU0LjUgMTRsMS4wNDUtLjM4N2EzLjUgMy41IDAgMCAwIDIuMDY4LTIuMDY4TDU4IDEwLjV6IiBmaWxsPSIjMDAwIi8+PC9zdmc+);
  --icon-status-blank-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zOC45OSAyMy45MzJhMiAyIDAgMCAxIDIuNDUtMS40MTVsMTguODk5IDUuMDY0YTIgMiAwIDAgMSAxLjQxNiAyLjQ0TDU2LjUgNTAuMDI2IDQ3Ljk5OSA1NGwtMTQuMDI0LTMuNDg0YTIgMiAwIDAgMS0xLjQ1LTIuNDU4bDYuNDY1LTI0LjEyNnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjgzMjkgLjg0NjUgLjg4NzEpIi8+PHBhdGggZD0iTTQ5LjM4MiA0OS44NTRMNDggNTRsOC41LTQtNC42MzItMS40MjVhMiAyIDAgMCAwLTIuNDg2IDEuMjc5eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuNzM3NCAuNzU4NyAuODIyNikiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTIuMDc4IDE5LjA5NmEyIDIgMCAwIDEgMS40MTQtMi40NWwyNy4zMTgtNy4zMmEyIDIgMCAwIDEgMi40NSAxLjQxNGw5LjAwNSAzMy42MDhhMiAyIDAgMCAxLTEuNDE0IDIuNDVsLTI3LjMxOCA3LjMyYTIgMiAwIDAgMS0yLjQ1LTEuNDE1TDIuMDc4IDE5LjA5NnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzggLjkxOTIgLjk0NjIpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05LjgzIDI0Ljk2M2wxOS4zMTgtNS4xNzYgMS4wMzYgMy44NjQtMTkuMzE5IDUuMTc2LTEuMDM1LTMuODY0em0xLjU1MyA1Ljc5NmwxMC40NDEtMi43OTggMS4wMzUgMy44NjQtMTAuNDQxIDIuNzk4LTEuMDM1LTMuODY0eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODA4OSAuODI2NyAuODcxMSkiLz48cGF0aCBkPSJNNTggMTAuNWwuMzg3IDEuMDQ1YTMuNSAzLjUgMCAwIDAgMi4wNjggMi4wNjhMNjEuNSAxNGwtMS4wNDUuMzg3YTMuNSAzLjUgMCAwIDAtMi4wNjggMi4wNjhMNTggMTcuNWwtLjM4Ny0xLjA0NWEzLjUgMy41IDAgMCAwLTIuMDY4LTIuMDY4TDU0LjUgMTRsMS4wNDUtLjM4N2EzLjUgMy41IDAgMCAwIDIuMDY4LTIuMDY4TDU4IDEwLjV6IiBmaWxsPSIjMDAwIi8+PC9zdmc+);
  --icon-status-chart-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2Ljc3IDkuNzNsMy4zNDIgMy4zNDJTMTguNjggMzEuNTg2IDQuNSAzMS41ODZjMTUuMjMgMCAzMC4wNjgtMTQuMDU3IDMwLjA2OC0xNC4wNTdsMy4zNDIgMy4zNDIgMS45NTMtMTMuMDk0TDI2Ljc3IDkuNzN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44NzA2IC44Nzg0IC45MDk4KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEgNDlWMzRoMTF2MTVIMTF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC43NjA3IC43Nzg2IC44MzkzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjYgNDlWMjhoMTF2MjFIMjZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44MzE0IC44NDcxIC44ODYzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNDEgNDguOTQzVjE4LjI3aDExdjMwLjY3M0g0MXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzcgLjkxNzYgLjk0NTEpIi8+PHBhdGggZD0iTTEwIDUzaDQ0IiBzdHJva2U9ImNvbG9yKGRpc3BsYXktcDMgLjgxNTcgLjgzMTQgLjg3NDUpIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz48L3N2Zz4=);
  --icon-status-chart-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2Ljc3IDkuNzNsMy4zNDIgMy4zNDJTMTguNjggMzEuNTg2IDQuNSAzMS41ODZjMTUuMjMgMCAzMC4wNjgtMTQuMDU3IDMwLjA2OC0xNC4wNTdsMy4zNDIgMy4zNDIgMS45NTMtMTMuMDk0TDI2Ljc3IDkuNzN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44NzA2IC44Nzg0IC45MDk4KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEgNDlWMzRoMTF2MTVIMTF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC43NjA3IC43Nzg2IC44MzkzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjYgNDlWMjhoMTF2MjFIMjZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44MzE0IC44NDcxIC44ODYzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNDEgNDguOTQzVjE4LjI3aDExdjMwLjY3M0g0MXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzcgLjkxNzYgLjk0NTEpIi8+PHBhdGggZD0iTTEwIDUzaDQ0IiBzdHJva2U9ImNvbG9yKGRpc3BsYXktcDMgLjgxNTcgLjgzMTQgLjg3NDUpIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz48L3N2Zz4=);
  --icon-status-loading-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjQiIHN0cm9rZT0iY29sb3IoZGlzcGxheS1wMyAuOTEzNyAuOTE3NiAuOTQ1MSkiIHN0cm9rZS13aWR0aD0iMyIvPjxwYXRoIGQ9Ik01NiAzMkM1NiAxOC43NDUgNDUuMjU1IDggMzIgOCIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC43MzczIC43NTY5IC44MjM1KSIgc3Ryb2tlLXdpZHRoPSIzIi8+PC9zdmc+);
  --icon-status-loading-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjQiIHN0cm9rZT0iY29sb3IoZGlzcGxheS1wMyAuOTEzNyAuOTE3NiAuOTQ1MSkiIHN0cm9rZS13aWR0aD0iMyIvPjxwYXRoIGQ9Ik01NiAzMkM1NiAxOC43NDUgNDUuMjU1IDggMzIgOCIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC43MzczIC43NTY5IC44MjM1KSIgc3Ryb2tlLXdpZHRoPSIzIi8+PC9zdmc+);
  --icon-status-network-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjUiIGhlaWdodD0iNjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zNC4zNDYgMjYuMDMxTDMzIDMwLjI5aDUuMDJsLTEuMTY2IDUuNjI2Yy01LjY0NS0xLjMxOS0xMS43ODQuMzU0LTE2LjE3NSA1LjAybC02LjMwOC02LjcwNGM1LjQ4OS01LjgzMyAxMi43ODktOC41NjYgMTkuOTc1LTguMnptMS4zNTYtNC4yOWMtOC42OTMtLjgxNC0xNy42NTEgMi4zMTQtMjQuMzA0IDkuMzg0TDUgMjQuMzI2YzkuMTcyLTkuNzQ3IDIxLjcyNC0xMy43MTMgMzMuNjQ2LTExLjg5OWwtMi45NDQgOS4zMTR6bTguNDY4IDYuODQzbC0zLjcxNyA4LjYyM2ExNy41MiAxNy41MiAwIDAgMSA1LjAxIDMuNzI3bDYuMzEtNi43MDQtLjMxMi0uMzI2YTI2LjM3NyAyNi4zNzcgMCAwIDAtNy4yOTEtNS4zMnptLTguMzM0IDEyLjI0Yy00LjEyMS0uOTk4LTguNjIuMjA5LTExLjgzIDMuNjJMMzMgNTRsLjEzNC0uMTQzIDIuNzAzLTEzLjAzM3pNMzMuNCA1My41NzRsNS4wNzYtMTEuNzc2YTEyLjczIDEyLjczIDAgMCAxIDMuNTE3IDIuNjQ1bC04LjU5MyA5LjEzMnptOC43NjYtMzAuNDcxbDMuMDU2LTkuMDU5YzUuNTc1IDEuOTE2IDEwLjgzIDUuMTggMTUuMzEyIDkuNzkxbC40NjcuNDg5LTYuMzk4IDYuNzk5Yy0zLjYwNS0zLjgzMS03Ljg4Ny02LjUwNS0xMi40MzctOC4wMnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjg4MjQgLjg5MDIgLjkxNzYpIi8+PGNpcmNsZSBjeD0iNDYiIGN5PSI0OC4wOTQiIHI9IjciIGZpbGw9IiMwMDAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTQ2LjcwNyA1MS45MThoLTEuNDEydi0xLjQxMmgxLjQxMnYxLjQxMnpNNDcgNDQuMDk0bC0uMjM4IDUuMDVoLTEuNTI0TDQ1IDQ0LjA5NWgyeiIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg==);
  --icon-status-network-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjUiIGhlaWdodD0iNjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zNC4zNDYgMjYuMDMxTDMzIDMwLjI5aDUuMDJsLTEuMTY2IDUuNjI2Yy01LjY0NS0xLjMxOS0xMS43ODQuMzU0LTE2LjE3NSA1LjAybC02LjMwOC02LjcwNGM1LjQ4OS01LjgzMyAxMi43ODktOC41NjYgMTkuOTc1LTguMnptMS4zNTYtNC4yOWMtOC42OTMtLjgxNC0xNy42NTEgMi4zMTQtMjQuMzA0IDkuMzg0TDUgMjQuMzI2YzkuMTcyLTkuNzQ3IDIxLjcyNC0xMy43MTMgMzMuNjQ2LTExLjg5OWwtMi45NDQgOS4zMTR6bTguNDY4IDYuODQzbC0zLjcxNyA4LjYyM2ExNy41MiAxNy41MiAwIDAgMSA1LjAxIDMuNzI3bDYuMzEtNi43MDQtLjMxMi0uMzI2YTI2LjM3NyAyNi4zNzcgMCAwIDAtNy4yOTEtNS4zMnptLTguMzM0IDEyLjI0Yy00LjEyMS0uOTk4LTguNjIuMjA5LTExLjgzIDMuNjJMMzMgNTRsLjEzNC0uMTQzIDIuNzAzLTEzLjAzM3pNMzMuNCA1My41NzRsNS4wNzYtMTEuNzc2YTEyLjczIDEyLjczIDAgMCAxIDMuNTE3IDIuNjQ1bC04LjU5MyA5LjEzMnptOC43NjYtMzAuNDcxbDMuMDU2LTkuMDU5YzUuNTc1IDEuOTE2IDEwLjgzIDUuMTggMTUuMzEyIDkuNzkxbC40NjcuNDg5LTYuMzk4IDYuNzk5Yy0zLjYwNS0zLjgzMS03Ljg4Ny02LjUwNS0xMi40MzctOC4wMnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjg4MjQgLjg5MDIgLjkxNzYpIi8+PGNpcmNsZSBjeD0iNDYiIGN5PSI0OC4wOTQiIHI9IjciIGZpbGw9IiMwMDAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTQ2LjcwNyA1MS45MThoLTEuNDEydi0xLjQxMmgxLjQxMnYxLjQxMnpNNDcgNDQuMDk0bC0uMjM4IDUuMDVoLTEuNTI0TDQ1IDQ0LjA5NWgyeiIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg==);
  --icon-status-no-permission-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjYiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzggLjkxOTIgLjk0NjIpIi8+PHBhdGggZD0iTTM4LjYyIDI2LjcxNnYtMy4wNDNhNi42NjMgNi42NjMgMCAwIDAtNi42NjQtNi42NjN2MGE2LjY2MyA2LjY2MyAwIDAgMC02LjY2MyA2LjY2M3YzLjA0MyIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC44MTU3IC44MzE0IC44NzQ1KSIgc3Ryb2tlLXdpZHRoPSIzLjk5OCIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIvPjxwYXRoIGQ9Ik0xOS40NjUgMjguOTA4aDI0Ljk4OHYxNi42NTlIMTkuNDY1VjI4LjkwOHoiIGZpbGw9IiNmZmYiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTMxLjI0MiA0Mi4wMTRWMzcuODVoMS40Mjh2NC4xNjRoLTEuNDI4eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMDU4OCAuMDU4OCAuMDcwNikiLz48cGF0aCBkPSJNMjkuNzM0IDM0LjkyN2EyLjE0MyAyLjE0MyAwIDEgMCA0LjI4NiAwIDIuMTQzIDIuMTQzIDAgMCAwLTQuMjg2IDB6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4wNTg4IC4wNTg4IC4wNzA2KSIvPjwvc3ZnPg==);
  --icon-status-no-permission-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjYiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzggLjkxOTIgLjk0NjIpIi8+PHBhdGggZD0iTTM4LjYyIDI2LjcxNnYtMy4wNDNhNi42NjMgNi42NjMgMCAwIDAtNi42NjQtNi42NjN2MGE2LjY2MyA2LjY2MyAwIDAgMC02LjY2MyA2LjY2M3YzLjA0MyIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC44MTU3IC44MzE0IC44NzQ1KSIgc3Ryb2tlLXdpZHRoPSIzLjk5OCIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIvPjxwYXRoIGQ9Ik0xOS40NjUgMjguOTA4aDI0Ljk4OHYxNi42NTlIMTkuNDY1VjI4LjkwOHoiIGZpbGw9IiNmZmYiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTMxLjI0MiA0Mi4wMTRWMzcuODVoMS40Mjh2NC4xNjRoLTEuNDI4eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMDU4OCAuMDU4OCAuMDcwNikiLz48cGF0aCBkPSJNMjkuNzM0IDM0LjkyN2EyLjE0MyAyLjE0MyAwIDEgMCA0LjI4NiAwIDIuMTQzIDIuMTQzIDAgMCAwLTQuMjg2IDB6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4wNTg4IC4wNTg4IC4wNzA2KSIvPjwvc3ZnPg==);
  --icon-status-pay-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05Ljk2NyA0Mi4xYTEgMSAwIDAgMS0uNzA3LTEuMjI0bDguMjgyLTMwLjkxYTEgMSAwIDAgMSAxLjIyNS0uNzA3TDYwLjMgMjAuMzg4YTEgMSAwIDAgMSAuNzA4IDEuMjI1bC04LjI4MyAzMC45MWExIDEgMCAwIDEtMS4yMjQuNzA2TDkuOTY3IDQyLjF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44MjA4IC44Mzc1IC44NzkyKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOSA1M2ExIDEgMCAwIDEtMS0xVjIwYTEgMSAwIDAgMSAxLTFoNDNhMSAxIDAgMCAxIDEgMXYzMmExIDEgMCAwIDEtMSAxSDl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC45MjYyIC45MzA4IC45NTM4KSIvPjxwYXRoIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjA1ODggLjA1ODggLjA3MDYpIiBkPSJNMTQgNDVoMnYyaC0yem01IDBoMnYyaC0yeiIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAzNXYtOWg0NXY5SDh6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44NTQ5IC44NjY3IC45MDIpIi8+PHBhdGggZD0iTTQ4IDQwaC03djdoN3YtN3oiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjgxOTYgLjgzNTMgLjg3ODQpIi8+PC9zdmc+);
  --icon-status-pay-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05Ljk2NyA0Mi4xYTEgMSAwIDAgMS0uNzA3LTEuMjI0bDguMjgyLTMwLjkxYTEgMSAwIDAgMSAxLjIyNS0uNzA3TDYwLjMgMjAuMzg4YTEgMSAwIDAgMSAuNzA4IDEuMjI1bC04LjI4MyAzMC45MWExIDEgMCAwIDEtMS4yMjQuNzA2TDkuOTY3IDQyLjF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44MjA4IC44Mzc1IC44NzkyKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOSA1M2ExIDEgMCAwIDEtMS0xVjIwYTEgMSAwIDAgMSAxLTFoNDNhMSAxIDAgMCAxIDEgMXYzMmExIDEgMCAwIDEtMSAxSDl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC45MjYyIC45MzA4IC45NTM4KSIvPjxwYXRoIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjA1ODggLjA1ODggLjA3MDYpIiBkPSJNMTQgNDVoMnYyaC0yem01IDBoMnYyaC0yeiIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAzNXYtOWg0NXY5SDh6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44NTQ5IC44NjY3IC45MDIpIi8+PHBhdGggZD0iTTQ4IDQwaC03djdoN3YtN3oiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjgxOTYgLjgzNTMgLjg3ODQpIi8+PC9zdmc+);
  --icon-status-search-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODMxNCAuODQ3MSAuODg2MykiIGQ9Ik05IDEzaDQ0djExSDl6Ii8+PHBhdGggZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuOTAxNSAuOTA3NyAuOTM4NSkiIGQ9Ik05IDI2aDExdjI1SDl6bTEzIDBoMzF2MjVIMjJ6Ii8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00NS43NSA0OS41ODNhNS44MzMgNS44MzMgMCAxIDAgMC0xMS42NjYgNS44MzMgNS44MzMgMCAwIDAgMCAxMS42NjZ6bTAgMS42NjdhNy41IDcuNSAwIDEgMCAwLTE1IDcuNSA3LjUgMCAwIDAgMCAxNXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjA1ODggLjA1ODggLjA3MDYpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik01NS4xNiA1NC4zMzlsLTUtNSAxLjE3OS0xLjE3OSA1IDUtMS4xNzkgMS4xNzl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4wNTg4IC4wNTg4IC4wNzA2KSIvPjwvc3ZnPg==);
  --icon-status-search-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODMxNCAuODQ3MSAuODg2MykiIGQ9Ik05IDEzaDQ0djExSDl6Ii8+PHBhdGggZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuOTAxNSAuOTA3NyAuOTM4NSkiIGQ9Ik05IDI2aDExdjI1SDl6bTEzIDBoMzF2MjVIMjJ6Ii8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00NS43NSA0OS41ODNhNS44MzMgNS44MzMgMCAxIDAgMC0xMS42NjYgNS44MzMgNS44MzMgMCAwIDAgMCAxMS42NjZ6bTAgMS42NjdhNy41IDcuNSAwIDEgMCAwLTE1IDcuNSA3LjUgMCAwIDAgMCAxNXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjA1ODggLjA1ODggLjA3MDYpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik01NS4xNiA1NC4zMzlsLTUtNSAxLjE3OS0xLjE3OSA1IDUtMS4xNzkgMS4xNzl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4wNTg4IC4wNTg4IC4wNzA2KSIvPjwvc3ZnPg==);
  --icon-status-upload-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3LjQ4NiAyNS41ODZhMS4yMiAxLjIyIDAgMCAxLS44OTQtLjkzIDE2LjA2IDE2LjA2IDAgMCAwLTUuMjUzLTguOTEzIDE1Ljg2NyAxNS44NjcgMCAwIDAtMTAuMzQtMy44MzdjLTMuNzkgMC03LjQ1NSAxLjM2LTEwLjMzOCAzLjgzN2ExNi4wNiAxNi4wNiAwIDAgMC01LjI1MyA4LjkxMyAxLjIyIDEuMjIgMCAwIDEtLjg5NS45MyAxMy4yNzEgMTMuMjcxIDAgMCAwLTcuMzU3IDQuOTM1IDEzLjQzIDEzLjQzIDAgMCAwIDEuNjY4IDE3Ljg5IDEzLjIyNiAxMy4yMjYgMCAwIDAgOC45MTQgMy40OTVINDQuMjg3YTEzLjIyNyAxMy4yMjcgMCAwIDAgOC45MDMtMy41MDkgMTMuNDMgMTMuNDMgMCAwIDAgMS42NDktMTcuODgyIDEzLjI3IDEzLjI3IDAgMCAwLTcuMzU0LTQuOTN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44ODI0IC44OTAyIC45MTc2KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjcuMjQyIDQ2LjgxM1YzNy41aC02LjM4N2wxMC42NDYtMTMuNzNMNDIuMTQ1IDM3LjVoLTYuMzg3djkuMzEzaC04LjUxNnptOC41MTYgMS4wNTVoLTguNTE2djQuMDM4aDguNTE2di00LjAzOHoiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNOC41IDEzbC40NDIgMS4xOTVhNCA0IDAgMCAwIDIuMzYzIDIuMzYzTDEyLjUgMTdsLTEuMTk1LjQ0MmE0IDQgMCAwIDAtMi4zNjMgMi4zNjNMOC41IDIxbC0uNDQyLTEuMTk1YTQgNCAwIDAgMC0yLjM2My0yLjM2M0w0LjUgMTdsMS4xOTUtLjQ0MmE0IDQgMCAwIDAgMi4zNjMtMi4zNjNMOC41IDEzeiIgZmlsbD0iIzAwMCIvPjwvc3ZnPg==);
  --icon-status-upload-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3LjQ4NiAyNS41ODZhMS4yMiAxLjIyIDAgMCAxLS44OTQtLjkzIDE2LjA2IDE2LjA2IDAgMCAwLTUuMjUzLTguOTEzIDE1Ljg2NyAxNS44NjcgMCAwIDAtMTAuMzQtMy44MzdjLTMuNzkgMC03LjQ1NSAxLjM2LTEwLjMzOCAzLjgzN2ExNi4wNiAxNi4wNiAwIDAgMC01LjI1MyA4LjkxMyAxLjIyIDEuMjIgMCAwIDEtLjg5NS45MyAxMy4yNzEgMTMuMjcxIDAgMCAwLTcuMzU3IDQuOTM1IDEzLjQzIDEzLjQzIDAgMCAwIDEuNjY4IDE3Ljg5IDEzLjIyNiAxMy4yMjYgMCAwIDAgOC45MTQgMy40OTVINDQuMjg3YTEzLjIyNyAxMy4yMjcgMCAwIDAgOC45MDMtMy41MDkgMTMuNDMgMTMuNDMgMCAwIDAgMS42NDktMTcuODgyIDEzLjI3IDEzLjI3IDAgMCAwLTcuMzU0LTQuOTN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44ODI0IC44OTAyIC45MTc2KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjcuMjQyIDQ2LjgxM1YzNy41aC02LjM4N2wxMC42NDYtMTMuNzNMNDIuMTQ1IDM3LjVoLTYuMzg3djkuMzEzaC04LjUxNnptOC41MTYgMS4wNTVoLTguNTE2djQuMDM4aDguNTE2di00LjAzOHoiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNOC41IDEzbC40NDIgMS4xOTVhNCA0IDAgMCAwIDIuMzYzIDIuMzYzTDEyLjUgMTdsLTEuMTk1LjQ0MmE0IDQgMCAwIDAtMi4zNjMgMi4zNjNMOC41IDIxbC0uNDQyLTEuMTk1YTQgNCAwIDAgMC0yLjM2My0yLjM2M0w0LjUgMTdsMS4xOTUtLjQ0MmE0IDQgMCAwIDAgMi4zNjMtMi4zNjNMOC41IDEzeiIgZmlsbD0iIzAwMCIvPjwvc3ZnPg==);
  --icon-status-error-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0yOC4yMSAxNC44MDZMOC4yOSAxNi4yMjZhMS4wNjcgMS4wNjcgMCAwIDAtLjk4OCAxLjE0MmwyLjM1NSAzMy4wMDhjLjA0Mi41ODkuNTUyIDEuMDMxIDEuMTQuOTlsMTMuOTc1LS45OTggOS4xMjQtMTQuMDk2LTExLjg2OC03Ljk5NCA2LjE4LTEzLjQ3MnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzggLjkxOTIgLjk0NjIpIi8+PHBhdGggZD0iTTcuMjkzIDE3LjM2N2ExIDEgMCAwIDEgLjkyNC0xLjA3NGwxOS45ODYtMS40NS0yLjgyNyA2LjA5LTE3LjcwMiAxLjM4LS4zODEtNC45NDZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44NDQ3IC44NTkyIC44OTUzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjUuMTc4IDUwLjI3MmwyNS4zMjUuNjE2YTEuMDY4IDEuMDY4IDAgMCAwIDEuMDkzLTEuMDQybC44MDQtMzMuMDgzYTEuMDY4IDEuMDY4IDAgMCAwLTEuMDQxLTEuMDkzbC0xNS4yNjUtLjM3LTkuMjQzIDEyLjU0IDExLjM5NiA4LjA5LTEzLjA2OSAxNC4zNDJ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC45MTM4IC45MTkyIC45NDYyKSIvPjxwYXRoIGQ9Ik0zNi4xMiAxNS4zMTJsMTUuMzE2LjM2N2ExIDEgMCAwIDEgLjk3NSAxLjAyN2wtLjEzNyA0LjkxNy0yMC4zOC0uNjYzIDQuMjI2LTUuNjQ4eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODQ0NyAuODU5MiAuODk1MykiLz48Y2lyY2xlIGN4PSI0OS41IiBjeT0iNDciIHI9IjciIGZpbGw9IiMwMDAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUwLjIwNyA1MC44MjRoLTEuNDEydi0xLjQxMWgxLjQxMnYxLjQxMXpNNTAuNSA0M2wtLjIzOCA1LjA1MWgtMS41MjRMNDguNSA0M2gyeiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0zNC40NDQgNy4yMDhsLTEuMjQ4IDUuNzI4Yy0uMDYzLjI4OC4zMzEuNDM1LjQ3My4xNzdsMi43ODctNS4wOTVhLjI1NS4yNTUgMCAwIDAtLjEyNy0uMzU4bC0xLjUzOC0uNjM0YS4yNTUuMjU1IDAgMCAwLS4zNDcuMTgyem0tNy44MzIgMi41NTZsMi42MTggMy4zM2MuMTc4LjIyNy41MzcuMDMuNDQtLjI0MmwtMS4zODctMy45NDVhLjI1NS4yNTUgMCAwIDAtLjM1NS0uMTQ0bC0xLjIzLjYxNWEuMjU1LjI1NSAwIDAgMC0uMDg2LjM4NnoiIGZpbGw9IiMwMDAiLz48L3N2Zz4=);
  --icon-status-error-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0yOC4yMSAxNC44MDZMOC4yOSAxNi4yMjZhMS4wNjcgMS4wNjcgMCAwIDAtLjk4OCAxLjE0MmwyLjM1NSAzMy4wMDhjLjA0Mi41ODkuNTUyIDEuMDMxIDEuMTQuOTlsMTMuOTc1LS45OTggOS4xMjQtMTQuMDk2LTExLjg2OC03Ljk5NCA2LjE4LTEzLjQ3MnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkxMzggLjkxOTIgLjk0NjIpIi8+PHBhdGggZD0iTTcuMjkzIDE3LjM2N2ExIDEgMCAwIDEgLjkyNC0xLjA3NGwxOS45ODYtMS40NS0yLjgyNyA2LjA5LTE3LjcwMiAxLjM4LS4zODEtNC45NDZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44NDQ3IC44NTkyIC44OTUzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjUuMTc4IDUwLjI3MmwyNS4zMjUuNjE2YTEuMDY4IDEuMDY4IDAgMCAwIDEuMDkzLTEuMDQybC44MDQtMzMuMDgzYTEuMDY4IDEuMDY4IDAgMCAwLTEuMDQxLTEuMDkzbC0xNS4yNjUtLjM3LTkuMjQzIDEyLjU0IDExLjM5NiA4LjA5LTEzLjA2OSAxNC4zNDJ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC45MTM4IC45MTkyIC45NDYyKSIvPjxwYXRoIGQ9Ik0zNi4xMiAxNS4zMTJsMTUuMzE2LjM2N2ExIDEgMCAwIDEgLjk3NSAxLjAyN2wtLjEzNyA0LjkxNy0yMC4zOC0uNjYzIDQuMjI2LTUuNjQ4eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODQ0NyAuODU5MiAuODk1MykiLz48Y2lyY2xlIGN4PSI0OS41IiBjeT0iNDciIHI9IjciIGZpbGw9IiMwMDAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUwLjIwNyA1MC44MjRoLTEuNDEydi0xLjQxMWgxLjQxMnYxLjQxMXpNNTAuNSA0M2wtLjIzOCA1LjA1MWgtMS41MjRMNDguNSA0M2gyeiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0zNC40NDQgNy4yMDhsLTEuMjQ4IDUuNzI4Yy0uMDYzLjI4OC4zMzEuNDM1LjQ3My4xNzdsMi43ODctNS4wOTVhLjI1NS4yNTUgMCAwIDAtLjEyNy0uMzU4bC0xLjUzOC0uNjM0YS4yNTUuMjU1IDAgMCAwLS4zNDcuMTgyem0tNy44MzIgMi41NTZsMi42MTggMy4zM2MuMTc4LjIyNy41MzcuMDMuNDQtLjI0MmwtMS4zODctMy45NDVhLjI1NS4yNTUgMCAwIDAtLjM1NS0uMTQ0bC0xLjIzLjYxNWEuMjU1LjI1NSAwIDAgMC0uMDg2LjM4NnoiIGZpbGw9IiMwMDAiLz48L3N2Zz4=);
  --icon-status-positive-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iMTAiIHk9IjEwLjkwNiIgd2lkdGg9IjM4IiBoZWlnaHQ9IjQwIiByeD0iMSIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuOTEzOCAuOTE5MiAuOTQ2MikiLz48cGF0aCBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44MDg5IC44MjY3IC44NzExKSIgZD0iTTE3IDIwLjkwNmgyNHY0SDE3em0wIDZoMTJ2NEgxN3oiLz48cGF0aCBkPSJNNTMuNSA2LjkwNmwuMzMyLjg5NmEzIDMgMCAwIDAgMS43NzIgMS43NzNsLjg5Ni4zMzEtLjg5Ni4zMzJhMyAzIDAgMCAwLTEuNzcyIDEuNzcybC0uMzMyLjg5Ni0uMzMyLS44OTZhMyAzIDAgMCAwLTEuNzcyLTEuNzcybC0uODk2LS4zMzIuODk2LS4zMzFhMyAzIDAgMCAwIDEuNzcyLTEuNzczbC4zMzItLjg5NnoiIGZpbGw9IiMwMDAiLz48cGF0aCBkPSJNNDUuNSAzMS4zMTNsNC4wNjEgNC4xOTUgNS44MzktLjA5NS0uMDk2IDUuODM5IDQuMTk2IDQuMDYtNC4xOTYgNC4wNjIuMDk2IDUuODM4LTUuODM5LS4wOTUtNC4wNjEgNC4xOTYtNC4wNjEtNC4xOTYtNS44MzkuMDk1LjA5Ni01LjgzOS00LjE5Ni00LjA2IDQuMTk2LTQuMDYyLS4wOTYtNS44MzggNS44MzkuMDk1IDQuMDYxLTQuMTk2eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODA5IC44MjQ1IC44NzEpIi8+PHBhdGggZD0iTTQxLjUgNDQuMDUxbDMuMjE2IDMuMDY1IDUuNDY3LTUuMjEiIHN0cm9rZT0iIzAwMCIgc3Ryb2tlLXdpZHRoPSIxLjY3OSIvPjwvc3ZnPg==);
  --icon-status-positive-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iMTAiIHk9IjEwLjkwNiIgd2lkdGg9IjM4IiBoZWlnaHQ9IjQwIiByeD0iMSIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuOTEzOCAuOTE5MiAuOTQ2MikiLz48cGF0aCBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC44MDg5IC44MjY3IC44NzExKSIgZD0iTTE3IDIwLjkwNmgyNHY0SDE3em0wIDZoMTJ2NEgxN3oiLz48cGF0aCBkPSJNNTMuNSA2LjkwNmwuMzMyLjg5NmEzIDMgMCAwIDAgMS43NzIgMS43NzNsLjg5Ni4zMzEtLjg5Ni4zMzJhMyAzIDAgMCAwLTEuNzcyIDEuNzcybC0uMzMyLjg5Ni0uMzMyLS44OTZhMyAzIDAgMCAwLTEuNzcyLTEuNzcybC0uODk2LS4zMzIuODk2LS4zMzFhMyAzIDAgMCAwIDEuNzcyLTEuNzczbC4zMzItLjg5NnoiIGZpbGw9IiMwMDAiLz48cGF0aCBkPSJNNDUuNSAzMS4zMTNsNC4wNjEgNC4xOTUgNS44MzktLjA5NS0uMDk2IDUuODM5IDQuMTk2IDQuMDYtNC4xOTYgNC4wNjIuMDk2IDUuODM4LTUuODM5LS4wOTUtNC4wNjEgNC4xOTYtNC4wNjEtNC4xOTYtNS44MzkuMDk1LjA5Ni01LjgzOS00LjE5Ni00LjA2IDQuMTk2LTQuMDYyLS4wOTYtNS44MzggNS44MzkuMDk1IDQuMDYxLTQuMTk2eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuODA5IC44MjQ1IC44NzEpIi8+PHBhdGggZD0iTTQxLjUgNDQuMDUxbDMuMjE2IDMuMDY1IDUuNDY3LTUuMjEiIHN0cm9rZT0iIzAwMCIgc3Ryb2tlLXdpZHRoPSIxLjY3OSIvPjwvc3ZnPg==);
  --button-color-bg-solid-default-brand: var(--tea-color-bg-brand-default);
  --button-color-bg-solid-hover-brand: var(--tea-color-bg-brand-hover);
  --button-color-bg-solid-active-brand: var(--tea-color-bg-brand-active);
  --button-color-bg-solid-focus-brand: var(--tea-color-bg-brand-focus);
  --button-color-bg-solid-disabled-brand: var(--tea-color-bg-brand-disabled);
  --button-color-border-solid-default-brand: var(--tea-color-border-on-bg-brand-default);
  --button-color-border-solid-hover-brand: var(--tea-color-border-on-bg-brand-hover);
  --button-color-border-solid-active-brand: var(--tea-color-border-on-bg-brand-active);
  --button-color-border-solid-focus-brand: var(--tea-color-border-on-bg-brand-focus);
  --button-color-border-solid-disabled-brand: var(--tea-color-border-on-bg-brand-disabled);
  --button-color-text-solid-default-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-hover-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-active-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-focus-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-disabled-brand: var(--tea-color-text-on-bg-brand-disabled);
  --button-color-bg-solid-default-warning: var(--tea-color-bg-warning-default);
  --button-color-bg-solid-hover-warning: var(--tea-color-bg-warning-hover);
  --button-color-bg-solid-active-warning: var(--tea-color-bg-warning-active);
  --button-color-bg-solid-focus-warning: var(--tea-color-bg-warning-focus);
  --button-color-bg-solid-disabled-warning: var(--tea-color-bg-warning-disabled);
  --button-color-border-solid-default-warning: var(--tea-color-border-on-bg-warning-default);
  --button-color-border-solid-hover-warning: var(--tea-color-border-on-bg-warning-hover);
  --button-color-border-solid-active-warning: var(--tea-color-border-on-bg-warning-active);
  --button-color-border-solid-focus-warning: var(--tea-color-border-on-bg-warning-focus);
  --button-color-border-solid-disabled-warning: var(--tea-color-border-on-bg-warning-disabled);
  --button-color-text-solid-default-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-hover-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-active-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-focus-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-disabled-warning: var(--tea-color-text-on-bg-warning-disabled);
  --button-color-bg-solid-default-error: var(--tea-color-bg-error-default);
  --button-color-bg-solid-hover-error: var(--tea-color-bg-error-hover);
  --button-color-bg-solid-active-error: var(--tea-color-bg-error-active);
  --button-color-bg-solid-focus-error: var(--tea-color-bg-error-focus);
  --button-color-bg-solid-disabled-error: var(--tea-color-bg-error-disabled);
  --button-color-border-solid-default-error: var(--tea-color-border-on-bg-error-default);
  --button-color-border-solid-hover-error: var(--tea-color-border-on-bg-error-hover);
  --button-color-border-solid-active-error: var(--tea-color-border-on-bg-error-active);
  --button-color-border-solid-focus-error: var(--tea-color-border-on-bg-error-focus);
  --button-color-border-solid-disabled-error: var(--tea-color-border-on-bg-error-disabled);
  --button-color-text-solid-default-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-hover-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-active-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-focus-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-disabled-error: var(--tea-color-text-on-bg-error-disabled);
  --button-color-bg-outline-default-neutral: var(--tea-color-bg-primary-default);
  --button-color-bg-outline-hover-neutral: var(--tea-color-bg-primary-hover);
  --button-color-bg-outline-active-neutral: var(--tea-color-bg-primary-active);
  --button-color-bg-outline-focus-neutral: var(--tea-color-bg-primary-default);
  --button-color-bg-outline-disabled-neutral: var(--tea-color-bg-primary-default);
  --button-color-border-outline-default-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-hover-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-active-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-focus-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-disabled-neutral: var(--tea-color-border-primary-disabled);
  --button-color-text-outline-default-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-hover-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-active-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-focus-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-disabled-neutral: var(--tea-color-text-disabled);
  --button-size-height-sm: 24px;
  --button-size-height-md: var(--tea-form-height-default);
  --button-size-height-lg: 40px;
  --button-font-size-sm: var(--tea-font-size-default);
  --button-font-size-md: var(--tea-font-size-default);
  --button-font-size-lg: var(--tea-font-size-350);
  --button-shadow-outlined-neutral: 0px 2px 0px 0px hsla(0,0%,73.3%,.05);
  --button-shadow-solid-brand: 0px 2px 0px 0px rgba(var(--tea-color-palette-blue-rgb),.05);
  --button-shadow-solid-error: 0px 2px 0px 0px rgba(var(--tea-color-palette-red-rgb),.05);
  --button-shadow-solid-warning: 0px 2px 0px 0px rgba(var(--tea-color-palette-orange-rgb),.05);
  --button-color-bg-solid-default-neutral: var(--tea-color-palette-gray-14);
  --button-color-bg-solid-hover-neutral: var(--tea-color-palette-gray-11);
  --button-color-bg-solid-active-neutral: var(--tea-color-palette-gray-14);
  --button-color-bg-solid-focus-neutral: var(--tea-color-palette-gray-11);
  --button-color-bg-solid-disabled-neutral: var(--tea-color-palette-gray-11);
  --button-color-text-solid-default-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-hover-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-active-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-focus-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-disabled-neutral: var(--tea-color-text-on-bg-inverse-disabled,var(--tea-color-palette-white-70));
  --tag-font-size: var(--tea-font-size-default);
  --tag-line-height: 18px;
  --tag-group-gap: 4px;
  --tag-border-radius: calc((var(--tag-line-height) + 2px)/2);
  --tag-icon-dismiss-right: calc(var(--tea-space-100)*2 - 1px);
  --tag-padding: 0 7px 0 7px;
  --tag-edit-padding: 0 25px 0 10px;
  --tag-color-border-default: var(--tea-color-border-tertiary-default);
  --tag-color-border-brand: var(--tea-color-border-on-bg-brand-lighten-default);
  --tag-color-border-success: var(--tea-color-border-on-bg-success-lighten-default);
  --tag-color-border-warning: var(--tea-color-border-on-bg-warning-lighten-default);
  --tag-color-border-error: var(--tea-color-border-on-bg-error-lighten-default);
  --tag-color-text-default: var(--tea-color-text-tertiary);
  --tag-color-text-hightlight: var(--tea-color-text-on-bg-inverse-default);
  --tag-color-text-brand: var(--tea-color-text-on-bg-brand-lighten-default);
  --tag-color-text-success: var(--tea-color-text-on-bg-success-lighten-default);
  --tag-color-text-warning: var(--tea-color-text-on-bg-warning-lighten-default);
  --tag-color-text-error: var(--tea-color-text-on-bg-error-lighten-default);
  --tag-color-bg-bright-default: var(--tea-color-bg-tertiary-default);
  --tag-color-bg-bright-brand: var(--tea-color-bg-brand-lighten-default);
  --tag-color-bg-bright-success: var(--tea-color-bg-success-lighten-default);
  --tag-color-bg-bright-warning: var(--tea-color-bg-warning-lighten-default);
  --tag-color-bg-bright-error: var(--tea-color-bg-error-lighten-default);
  --tag-color-bg-darkness-default: var(--tea-color-bg-inverse-default);
  --tag-color-bg-darkness-brand: var(--tea-color-bg-brand-default);
  --tag-color-bg-darkness-success: var(--tea-color-bg-success-default);
  --tag-color-bg-darkness-warning: var(--tea-color-bg-warning-default);
  --tag-color-bg-darkness-error: var(--tea-color-bg-error-default);
  --tag-input-color-bg: var(--tea-form-color-bg-primary-default);
  --tag-input-color-border: var(--tea-color-border-primary-default);
  --list-item-color-bg-selected: var(--tea-color-bg-brand-default);
  --list-item-color-text-selected: var(--tea-color-text-on-bg-brand-default);
  --range-datepicker-container-width-format-1: 220px;
  --range-datepicker-container-width-format-2: 325px;
  --range-datepicker-container-width-format-3: 360px;
  --range-datepicker-cell-in-range-color-bg-default: var(--tea-color-bg-brand-lighten-default);
  --switch-dot-color-bg: var(--tea-color-bg-primary-default);
  --switch-color-bg-default: var(--tea-color-palette-bluegray-4);
  --switch-color-bg-hover: var(--tea-color-palette-bluegray-5);
  --switch-color-bg-disabled: var(--tea-color-palette-bluegray-4);
  --bubbles-font-size: var(--tea-font-size-default);
  --bubbles-border-radius: var(--tea-border-radius-default);
  --bubbles-width-max: 300px;
  --bubbles-triangle-width: 6px;
  --bubbles-border-width: 1px;
  --bubbles-color-bg-inverse: var(--tea-color-bg-inverse-default);
  --bubbles-color-bg-default: var(--tea-color-bg-primary-default);
  --bubbles-color-bg-error: var(--tea-color-bg-error-lighten-default);
  --bubbles-color-text-inverse: var(--tea-color-text-on-bg-inverse-default);
  --bubbles-color-text-default: var(--tea-color-text-primary);
  --bubbles-color-text-error: var(--tea-color-text-on-bg-error-lighten-default);
  --bubbles-color-border-inverse: var(--tea-color-bg-inverse-default);
  --bubbles-color-border-default: var(--tea-color-bg-primary-default);
  --bubbles-color-border-error: var(--tea-color-bg-error-lighten-default);
  --card-color-bg: var(--tea-color-bg-primary-default);
  --card-box-shadow-default: var(--tea-shadow-xs);
  --card-box-shadow-hover: var(--tea-shadow-sm);
  --card-size-border-default: 0px;
  --card-color-border-default: var(--card-color-bg);
  --card-bordered-color-border-default: var(--tea-color-border-primary-default);
  --card-color-text: var(--tea-color-text-primary);
  --card-color-text-maintitle: var(--tea-color-text-primary);
  --card-color-text-subtitle: var(--tea-color-text-tertiary);
  --card-border-radius: var(--tea-border-radius-default);
  --card-color-borde-inner: var(--tea-color-border-secondary-default);
  --card-title-margin-bottom: 18px;
  --card-multiple-margin-top: 20px;
  --card-font-size: var(--tea-font-size-default);
  --card-body-title-font-size: var(--tea-font-size-400);
  --card-body-padding: 24px 32px;
  --card-header-padding: 20px 32px;
  --dropdown-color-bg: var(--tea-form-color-bg-primary-default);
  --tooltips-border-radius: var(--tea-border-radius-default);
  --tooltips-color-bg-default: var(--tea-color-bg-inverse-default);
  --tooltips-color-border-default: var(--tea-color-bg-inverse-default);
  --tooltips-color-text-default: var(--tea-color-text-on-bg-inverse-default);
  --tooltips-color-bg-inverse: var(--tea-color-bg-primary-default);
  --tooltips-color-border-inverse: var(--tea-color-bg-primary-default);
  --tooltips-color-text-inverse: var(--tea-color-text-primary);
  --rate-color-bg-default: var(--tea-color-palette-bluegray-4);
  --alert-nav-color-bg: var(--tea-color-bg-primary-default);
  --alert-primary-color-bg: var(--tea-color-bg-brand-lighten-default);
  --alert-primary-color-border: var(--tea-color-bg-brand-lighten-default);
  --alert-primary-color-text: var(--tea-color-text-on-bg-brand-lighten-default);
  --alert-primary-color-icon: var(--tea-color-text-on-bg-brand-lighten-default);
  --alert-error-color-bg: var(--tea-color-bg-error-lighten-default);
  --alert-error-color-border: var(--tea-color-bg-error-lighten-default);
  --alert-error-color-text: var(--tea-color-text-on-bg-error-lighten-default);
  --alert-error-color-icon: var(--tea-color-text-on-bg-error-lighten-default);
  --alert-warning-color-bg: var(--tea-color-bg-warning-lighten-default);
  --alert-warning-color-border: var(--tea-color-bg-warning-lighten-default);
  --alert-warning-color-text: var(--tea-color-text-on-bg-warning-lighten-default);
  --alert-warning-color-icon: var(--tea-color-text-on-bg-warning-lighten-default);
  --alert-success-color-bg: var(--tea-color-bg-success-lighten-default);
  --alert-success-color-border: var(--tea-color-bg-success-lighten-default);
  --alert-success-color-text: var(--tea-color-text-on-bg-success-lighten-default);
  --alert-success-color-icon: var(--tea-color-text-on-bg-success-lighten-default);
  --alert-notice-color-bg: var(--tea-color-bg-primary-default);
  --alert-notice-color-bg-icon: var(--tea-color-palette-gray-1);
  --alert-notice-color-text: var(--tea-color-text-primary);
  --alert-notice-color-border: var(--tea-color-bg-primary-default);
  --segment-button-color-bg-default: var(--tea-form-color-bg-primary-default);
  --segment-button-color-bg-hover: var(--tea-form-color-bg-primary-hover);
  --segment-button-color-bg-selected: var(--tea-form-color-bg-primary-default);
  --segment-button-color-bg-disabled: var(--tea-form-color-bg-primary-disabled);
  --segment-button-color-border-default: var(--tea-color-border-primary-default);
  --segment-button-color-border-hover: var(--tea-color-border-primary-default);
  --segment-button-color-border-selected: var(--tea-color-border-brand-default);
  --segment-button-color-border-disabled: var(--tea-color-border-primary-default);
  --segment-button-color-text-default: var(--tea-color-text-primary);
  --segment-button-color-text-hover: var(--tea-color-text-primary);
  --segment-button-color-text-selected: var(--tea-color-text-brand-default);
  --segment-button-color-text-disabled: var(--tea-color-text-disabled);
  --step-num-color-bg-default: var(--tea-color-bg-brand-default);
  --step-num-color-bg-disabled: var(--tea-form-color-bg-primary-default);
  --step-num-color-border-default: var(--tea-color-border-on-bg-brand-default);
  --step-num-color-border-disabled: var(--tea-color-border-primary-default);
  --step-num-color-text-default: var(--tea-color-text-on-bg-brand-default);
  --step-num-color-text-disabled: var(--tea-color-text-disabled);
  --step-arrow-color-bg-default: var(--tea-color-border-primary-default);
  --step-num-color-bg-default-process: var(--tea-color-bg-brand-lighten-default);
  --step-num-color-bg-disabled-process: var(--tea-form-color-bg-primary-default);
  --step-num-color-border-default-process: var(--tea-color-bg-brand-lighten-default);
  --step-num-color-border-disabled-process: var(--tea-color-border-primary-default);
  --step-num-color-text-default-process: var(--tea-color-text-on-bg-brand-lighten-default);
  --step-num-color-text-disabled-process: var(--tea-color-text-on-bg-brand-lighten-default);
  --step-arrow-color-bg-default-process: var(--tea-color-border-primary-default);
  --tabs-color-text-default: var(--tea-color-text-secondary);
  --tabs-color-text-active: var(--tea-color-text-primary);
  --tabs-color-text-disabled: var(--tea-color-text-disabled);
  --tabs-color-border-default: var(--tea-color-border-primary-default);
  --tabs-color-border-active: var(--tea-color-border-brand-default);
  --tabs-height-default: var(--tea-form-height-default);
  --tabs-animation: .15s ease-in-out,width .15s ease-in-out,height .15s ease-in-out;
  --tabs-space-horizontal-outer: 16px;
  --tabs-item-color-bg-segment-default: var(--tea-color-bg-tertiary-default);
  --tabs-item-color-bg-segment-hover: var(--tea-color-bg-tertiary-default);
  --tabs-item-color-bg-segment-active: var(--tea-color-bg-primary-default);
  --table-color-bg-primary-default: var(--tea-color-bg-primary-default);
  --table-color-border-primary-default: var(--tea-color-border-primary-default);
  --table-color-border-secondary-default: var(--tea-color-border-secondary-default);
  --table-header-color-bg-primary-default: var(--tea-color-bg-primary-default);
  --table-header-color-bg-gray-default: var(--tea-color-bg-primary-lighten);
  --table-header-color-text-default: var(--tea-color-text-secondary);
  --table-header-font-weight: var(--tea-font-weight-medium);
  --table-cell-color-bg-hover: var(--tea-color-bg-primary-hover);
  --table-color-bg-primary-hover: var(--tea-color-bg-primary-hover);
  --table-color-bg-secondary-default: var(--tea-color-bg-secondary-default);
  --table-fake-bar-color-bg-default: var(--tea-color-palette-black-10);
  --table-fake-bar-color-bg-hover: var(--tea-color-palette-black-20);
  --table-nesting-color-bg-default: var(--tea-color-bg-primary-lighten);
  --table-cell-space-horizontal-md: 10px;
  --table-cell-space-horizontal-sm: 10px;
  --table-cell-space-vertical-md: 16px;
  --table-cell-space-vertical-sm: 12px;
  --table-action-panel-space-vertical: 16px;
  --table-color-mask-default: var(--tea-color-palette-white-80);
  --table-animation-duration: .1s;
  --menu-padding-both-sides: 16px;
  --menu-bg: var(--tea-color-bg-primary-default);
  --menu-border: var(--tea-color-border-secondary-default);
  --menu-text-default: var(--tea-color-text-primary);
  --ment-text-secondary: var(--tea-color-text-secondary);
  --menu-text-label: var(--tea-color-text-tertiary);
  --menu-item-bg-hover: var(--tea-color-bg-primary-hover);
  --menu-item-text-hover: var(--tea-color-text-primary);
  --menu-item-bg-active: var(--tea-color-bg-brand-default);
  --menu-item-text-active: var(--tea-color-text-on-bg-brand-default);
  --menu-badge-bg-default: var(--tea-color-bg-secondary-hover);
  --menu-badge-text-default: var(--tea-color-text-tertiary);
  --menu-icon-color: var(--tea-color-text-tertiary);
  --menu-icon-color-active: var(--tea-color-text-on-bg-brand-default);
  --menu-icon-jump-color-text: var(--tea-color-text-brand-default);
  --menu-icon-back-color-text: var(--tea-color-text-brand-default);
  --blank-color-bg: #f5f8fb;
  --notification-size-width-max: 368px;
}

@media(max-width:1024px) {
  .tea-theme-light,
  :root,
  [theme-mode=light] {
    --card-body-padding: 16px;
    --card-header-padding: 16px;
  }
}

.tea-theme-dark,
[theme-mode=dark][theme-enable=true] {
  color-scheme: dark;
  --tea-color-text-link-default: var(--tea-color-palette-blue-8);
  --tea-color-text-link-hover: var(--tea-color-palette-blue-9);
  --tea-color-text-link-active: var(--tea-color-palette-blue-9);
  --tea-color-text-link-focus: var(--tea-color-palette-blue-5);
  --tea-color-text-link-disabled: var(--tea-color-palette-blue-3);
  --tea-color-text-solid: var(--tea-color-palette-white-100);
  --tea-color-text-primary: var(--tea-color-palette-white-90);
  --tea-color-text-secondary: var(--tea-color-palette-white-70);
  --tea-color-text-tertiary: var(--tea-color-palette-white-50);
  --tea-color-text-disabled: var(--tea-color-palette-white-35);
  --tea-color-text-paragraph: var(--tea-color-palette-white-80);
  --tea-color-text-brand-default: var(--tea-color-palette-blue-8);
  --tea-color-text-brand-hover: var(--tea-color-palette-blue-5);
  --tea-color-text-brand-active: var(--tea-color-palette-blue-9);
  --tea-color-text-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-text-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-text-warning-default: var(--tea-color-palette-orange-6);
  --tea-color-text-warning-hover: var(--tea-color-palette-orange-5);
  --tea-color-text-warning-active: var(--tea-color-palette-orange-9);
  --tea-color-text-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-text-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-text-success-default: var(--tea-color-palette-green-6);
  --tea-color-text-success-hover: var(--tea-color-palette-green-5);
  --tea-color-text-success-active: var(--tea-color-palette-green-9);
  --tea-color-text-success-focus: var(--tea-color-palette-green-5);
  --tea-color-text-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-text-error-default: var(--tea-color-palette-red-6);
  --tea-color-text-error-hover: var(--tea-color-palette-red-5);
  --tea-color-text-error-active: var(--tea-color-palette-red-9);
  --tea-color-text-error-focus: var(--tea-color-palette-red-5);
  --tea-color-text-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-text-on-bg-brand-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-brand-disabled: var(--tea-color-palette-white-35);
  --tea-color-text-on-bg-brand-lighten-default: var(--tea-color-palette-blue-9);
  --tea-color-text-on-bg-warning-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-warning-disabled: var(--tea-color-palette-white-35);
  --tea-color-text-on-bg-warning-lighten-default: var(--tea-color-palette-orange-9);
  --tea-color-text-on-bg-success-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-success-disabled: var(--tea-color-palette-white-35);
  --tea-color-text-on-bg-success-lighten-default: var(--tea-color-palette-green-9);
  --tea-color-text-on-bg-error-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-error-disabled: var(--tea-color-palette-white-35);
  --tea-color-text-on-bg-error-lighten-default: var(--tea-color-palette-red-8);
  --tea-color-text-on-bg-inverse-default: var(--tea-color-palette-black-90);
  --tea-color-text-on-bg-amber-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-yellow-default: var(--tea-color-palette-white-90);
  --tea-color-text-on-bg-amber-lighten-default: var(--tea-color-palette-amber-9);
  --tea-color-text-on-bg-yellow-lighten-default: var(--tea-color-palette-yellow-8);
  --tea-color-palette-white-0: hsla(0,0%,100%,0);
  --tea-color-palette-white-5: hsla(0,0%,100%,.05);
  --tea-color-palette-white-10: hsla(0,0%,100%,.1);
  --tea-color-palette-white-15: hsla(0,0%,100%,.15);
  --tea-color-palette-white-20: hsla(0,0%,100%,.2);
  --tea-color-palette-white-25: hsla(0,0%,100%,.25);
  --tea-color-palette-white-30: hsla(0,0%,100%,.3);
  --tea-color-palette-white-35: hsla(0,0%,100%,.35);
  --tea-color-palette-white-40: hsla(0,0%,100%,.4);
  --tea-color-palette-white-45: hsla(0,0%,100%,.45);
  --tea-color-palette-white-50: hsla(0,0%,100%,.5);
  --tea-color-palette-white-55: hsla(0,0%,100%,.55);
  --tea-color-palette-white-60: hsla(0,0%,100%,.6);
  --tea-color-palette-white-70: hsla(0,0%,100%,.7);
  --tea-color-palette-white-80: hsla(0,0%,100%,.8);
  --tea-color-palette-white-90: hsla(0,0%,100%,.9);
  --tea-color-palette-white-100: #fff;
  --tea-color-palette-black-0: transparent;
  --tea-color-palette-black-5: rgba(0,0,0,.05);
  --tea-color-palette-black-10: rgba(0,0,0,.1);
  --tea-color-palette-black-15: rgba(0,0,0,.15);
  --tea-color-palette-black-20: rgba(0,0,0,.2);
  --tea-color-palette-black-25: rgba(0,0,0,.25);
  --tea-color-palette-black-30: rgba(0,0,0,.3);
  --tea-color-palette-black-35: rgba(0,0,0,.35);
  --tea-color-palette-black-40: rgba(0,0,0,.4);
  --tea-color-palette-black-45: rgba(0,0,0,.45);
  --tea-color-palette-black-50: rgba(0,0,0,.5);
  --tea-color-palette-black-55: rgba(0,0,0,.55);
  --tea-color-palette-black-60: rgba(0,0,0,.6);
  --tea-color-palette-black-70: rgba(0,0,0,.7);
  --tea-color-palette-black-80: rgba(0,0,0,.8);
  --tea-color-palette-black-90: rgba(0,0,0,.9);
  --tea-color-palette-black-100: #000;
  --tea-color-palette-red-1: #461523;
  --tea-color-palette-red-2: #960627;
  --tea-color-palette-red-3: #b01c37;
  --tea-color-palette-red-4: #c9384a;
  --tea-color-palette-red-5: #e35661;
  --tea-color-palette-red-6: #fb6e77;
  --tea-color-palette-red-7: #ff9195;
  --tea-color-palette-red-8: #ffb5b8;
  --tea-color-palette-red-9: #ffd6d8;
  --tea-color-palette-red-10: #fff2f2;
  --tea-color-palette-red-rgb: 255,181,184;
  --tea-color-palette-yellow-1: #271d04;
  --tea-color-palette-yellow-2: #413007;
  --tea-color-palette-yellow-3: #644e15;
  --tea-color-palette-yellow-4: #967420;
  --tea-color-palette-yellow-5: #d5a52d;
  --tea-color-palette-yellow-6: #f8c034;
  --tea-color-palette-yellow-7: #fbcd59;
  --tea-color-palette-yellow-8: #fcda86;
  --tea-color-palette-yellow-9: #fde7ae;
  --tea-color-palette-yellow-10: #ffefc7;
  --tea-color-palette-yellow-rgb: 252,218,134;
  --tea-color-palette-orange-1: #462415;
  --tea-color-palette-orange-2: #873105;
  --tea-color-palette-orange-3: #a24006;
  --tea-color-palette-orange-4: #c25110;
  --tea-color-palette-orange-5: #d66724;
  --tea-color-palette-orange-6: #ed8139;
  --tea-color-palette-orange-7: #ff9852;
  --tea-color-palette-orange-8: #ffb97d;
  --tea-color-palette-orange-9: #ffd8ad;
  --tea-color-palette-orange-10: #fff4e5;
  --tea-color-palette-orange-rgb: 255,185,125;
  --tea-color-palette-amber-1: #291c0c;
  --tea-color-palette-amber-2: #3d2912;
  --tea-color-palette-amber-3: #5e401e;
  --tea-color-palette-amber-4: #7a5324;
  --tea-color-palette-amber-5: #92632b;
  --tea-color-palette-amber-6: #b77c35;
  --tea-color-palette-amber-7: #cf8c3c;
  --tea-color-palette-amber-8: #f2a74e;
  --tea-color-palette-amber-9: #f7c07e;
  --tea-color-palette-amber-10: #fbdbb5;
  --tea-color-palette-amber-rgb: 242,167,78;
  --tea-color-palette-green-1: #223b30;
  --tea-color-palette-green-2: #035428;
  --tea-color-palette-green-3: #046939;
  --tea-color-palette-green-4: #057e4c;
  --tea-color-palette-green-5: #06935f;
  --tea-color-palette-green-6: #07a872;
  --tea-color-palette-green-7: #37bf8e;
  --tea-color-palette-green-8: #71d5ae;
  --tea-color-palette-green-9: #b3e8d1;
  --tea-color-palette-green-10: #e8f7f1;
  --tea-color-palette-green-rgb: 113,213,174;
  --tea-color-palette-blue-1: #1d2852;
  --tea-color-palette-blue-2: #062e9a;
  --tea-color-palette-blue-3: #073ab5;
  --tea-color-palette-blue-4: #084dcd;
  --tea-color-palette-blue-5: #0957d9;
  --tea-color-palette-blue-6: #2174ff;
  --tea-color-palette-blue-7: #478dff;
  --tea-color-palette-blue-8: #69a1ff;
  --tea-color-palette-blue-9: #8cb8ff;
  --tea-color-palette-blue-10: #abcaff;
  --tea-color-palette-blue-rgb: 105,161,255;
  --tea-color-palette-gray-1: #f3f3f3;
  --tea-color-palette-gray-2: #eee;
  --tea-color-palette-gray-3: #e7e7e7;
  --tea-color-palette-gray-4: #dcdcdc;
  --tea-color-palette-gray-5: #c5c5c5;
  --tea-color-palette-gray-6: #a6a6a6;
  --tea-color-palette-gray-7: #8b8b8b;
  --tea-color-palette-gray-8: #777;
  --tea-color-palette-gray-9: #5e5e5e;
  --tea-color-palette-gray-10: #4b4b4b;
  --tea-color-palette-gray-11: #383838;
  --tea-color-palette-gray-12: #2c2c2c;
  --tea-color-palette-gray-13: #202020;
  --tea-color-palette-gray-14: #181818;
  --tea-color-palette-gray-15: #101010;
  --tea-color-palette-bluegray-0: #f7f8fb;
  --tea-color-palette-bluegray-1: #f2f4f8;
  --tea-color-palette-bluegray-2: #e9ecf1;
  --tea-color-palette-bluegray-3: #e6e9ef;
  --tea-color-palette-bluegray-4: #d6dbe3;
  --tea-color-palette-bluegray-5: #bcc4d0;
  --tea-color-palette-bluegray-6: #97a3b7;
  --tea-color-palette-bluegray-7: #7787a2;
  --tea-color-palette-bluegray-8: #5f7292;
  --tea-color-palette-bluegray-9: #4b5b76;
  --tea-color-palette-bluegray-10: #3c485c;
  --tea-color-palette-bluegray-11: #2c3645;
  --tea-color-palette-bluegray-12: #232a35;
  --tea-color-palette-bluegray-13: #1c222b;
  --tea-color-palette-bluegray-14: #13161b;
  --tea-color-border-brand-default: var(--tea-color-palette-blue-8);
  --tea-color-border-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-border-warning-default: var(--tea-color-palette-orange-6);
  --tea-color-border-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-border-success-default: var(--tea-color-palette-green-6);
  --tea-color-border-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-border-error-default: var(--tea-color-palette-red-6);
  --tea-color-border-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-border-primary-default: var(--tea-color-palette-gray-11);
  --tea-color-border-primary-hover: var(--tea-color-palette-blue-8);
  --tea-color-border-primary-focus: var(--tea-color-palette-blue-8);
  --tea-color-border-primary-active: var(--tea-color-palette-blue-8);
  --tea-color-border-primary-disabled: var(--tea-color-palette-gray-11);
  --tea-color-border-secondary-default: var(--tea-color-palette-gray-12);
  --tea-color-border-secondary-hover: var(--tea-color-palette-gray-11);
  --tea-color-border-secondary-focus: var(--tea-color-palette-blue-8);
  --tea-color-border-secondary-active: var(--tea-color-palette-blue-8);
  --tea-color-border-secondary-disabled: var(--tea-color-palette-gray-10);
  --tea-color-border-tertiary-default: var(--tea-color-palette-gray-9);
  --tea-color-border-tertiary-hover: var(--tea-color-palette-gray-8);
  --tea-color-border-tertiary-focus: var(--tea-color-palette-blue-8);
  --tea-color-border-tertiary-active: var(--tea-color-palette-blue-8);
  --tea-color-border-tertiary-disabled: var(--tea-color-palette-gray-9);
  --tea-color-border-on-bg-yellow-default: var(--tea-color-palette-yellow-7);
  --tea-color-border-on-bg-yellow-hover: #fff;
  --tea-color-border-on-bg-yellow-active: #fff;
  --tea-color-border-on-bg-yellow-focus: #fff;
  --tea-color-border-on-bg-yellow-disabled: #fff;
  --tea-color-border-on-bg-yellow-lighten-default: var(--tea-color-palette-yellow-7);
  --tea-color-border-on-bg-amber-default: var(--tea-color-palette-amber-7);
  --tea-color-border-on-bg-amber-hover: #fff;
  --tea-color-border-on-bg-amber-active: #fff;
  --tea-color-border-on-bg-amber-focus: #fff;
  --tea-color-border-on-bg-amber-color: #fff;
  --tea-color-border-on-bg-amber-lighten-default: var(--tea-color-palette-amber-7);
  --tea-color-border-on-bg-brand-default: var(--tea-color-palette-blue-6);
  --tea-color-border-on-bg-brand-hover: var(--tea-color-palette-blue-7);
  --tea-color-border-on-bg-brand-active: var(--tea-color-palette-blue-4);
  --tea-color-border-on-bg-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-border-on-bg-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-border-on-bg-brand-lighten-default: var(--tea-color-palette-blue-5);
  --tea-color-border-on-bg-warning-default: var(--tea-color-palette-orange-6);
  --tea-color-border-on-bg-warning-hover: var(--tea-color-palette-orange-7);
  --tea-color-border-on-bg-warning-active: var(--tea-color-palette-orange-4);
  --tea-color-border-on-bg-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-border-on-bg-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-border-on-bg-warning-lighten-default: var(--tea-color-palette-orange-5);
  --tea-color-border-on-bg-success-default: var(--tea-color-palette-green-6);
  --tea-color-border-on-bg-success-hover: var(--tea-color-palette-green-7);
  --tea-color-border-on-bg-success-active: var(--tea-color-palette-green-4);
  --tea-color-border-on-bg-success-focus: var(--tea-color-palette-green-5);
  --tea-color-border-on-bg-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-border-on-bg-success-lighten-default: var(--tea-color-palette-green-5);
  --tea-color-border-on-bg-error-default: var(--tea-color-palette-red-6);
  --tea-color-border-on-bg-error-hover: var(--tea-color-palette-red-7);
  --tea-color-border-on-bg-error-active: var(--tea-color-palette-red-4);
  --tea-color-border-on-bg-error-focus: var(--tea-color-palette-red-5);
  --tea-color-border-on-bg-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-border-on-bg-error-lighten-default: var(--tea-color-palette-red-5);
  --tea-color-border-on-bg-inverse-default: var(--tea-color-palette-white-100);
  --tea-color-bg-page-default: var(--tea-color-palette-gray-14);
  --tea-color-bg-inverse-default: var(--tea-color-palette-white-100);
  --tea-color-bg-primary-default: var(--tea-color-palette-gray-13);
  --tea-color-bg-primary-hover: var(--tea-color-palette-gray-12);
  --tea-color-bg-primary-active: var(--tea-color-palette-gray-11);
  --tea-color-bg-primary-focus: var(--tea-color-palette-gray-12);
  --tea-color-bg-primary-disabled: var(--tea-color-palette-gray-12);
  --tea-color-bg-primary-lighten: var(--tea-color-palette-gray-12);
  --tea-color-bg-secondary-default: var(--tea-color-palette-gray-12);
  --tea-color-bg-secondary-hover: var(--tea-color-palette-gray-11);
  --tea-color-bg-secondary-active: var(--tea-color-palette-gray-11);
  --tea-color-bg-secondary-focus: var(--tea-color-palette-gray-11);
  --tea-color-bg-secondary-disabled: var(--tea-color-palette-gray-12);
  --tea-color-bg-tertiary-default: var(--tea-color-palette-gray-11);
  --tea-color-bg-tertiary-hover: var(--tea-color-palette-gray-10);
  --tea-color-bg-tertiary-active: var(--tea-color-palette-gray-10);
  --tea-color-bg-tertiary-focus: var(--tea-color-palette-gray-10);
  --tea-color-bg-tertiary-disabled: var(--tea-color-palette-gray-11);
  --tea-color-bg-brand-default: var(--tea-color-palette-blue-6);
  --tea-color-bg-brand-hover: var(--tea-color-palette-blue-7);
  --tea-color-bg-brand-active: var(--tea-color-palette-blue-4);
  --tea-color-bg-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-bg-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-bg-brand-lighten-default: var(--tea-color-palette-blue-1);
  --tea-color-bg-warning-default: var(--tea-color-palette-orange-6);
  --tea-color-bg-warning-hover: var(--tea-color-palette-orange-7);
  --tea-color-bg-warning-active: var(--tea-color-palette-orange-4);
  --tea-color-bg-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-bg-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-bg-warning-lighten-default: var(--tea-color-palette-orange-1);
  --tea-color-bg-success-default: var(--tea-color-palette-green-6);
  --tea-color-bg-success-hover: var(--tea-color-palette-green-7);
  --tea-color-bg-success-active: var(--tea-color-palette-green-4);
  --tea-color-bg-success-focus: var(--tea-color-palette-green-5);
  --tea-color-bg-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-bg-success-lighten-default: var(--tea-color-palette-green-1);
  --tea-color-bg-error-default: var(--tea-color-palette-red-6);
  --tea-color-bg-error-hover: var(--tea-color-palette-red-7);
  --tea-color-bg-error-active: var(--tea-color-palette-red-4);
  --tea-color-bg-error-focus: var(--tea-color-palette-red-5);
  --tea-color-bg-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-bg-error-lighten-default: var(--tea-color-palette-red-1);
  --tea-color-bg-amber-default: var(--tea-color-palette-amber-8);
  --tea-color-bg-amber-hover: #fff;
  --tea-color-bg-amber-active: #fff;
  --tea-color-bg-amber-focus: #fff;
  --tea-color-bg-amber-disabled: #fff;
  --tea-color-bg-amber-lighten-lighten: var(--tea-color-palette-amber-3);
  --tea-color-bg-yellow-default: var(--tea-color-palette-yellow-5);
  --tea-color-bg-yellow-hover: #fff;
  --tea-color-bg-yellow-focus: #fff;
  --tea-color-bg-yellow-active: #fff;
  --tea-color-bg-yellow-disabled: #fff;
  --tea-color-bg-yellow-lighten-lighten: var(--tea-color-palette-yellow-2);
  --tea-color-bg-scheme-mode: dark;
  --tea-color-mask-primary: var(--tea-color-palette-black-60);
  --tea-color-mask-secondary: var(--tea-color-palette-white-60);
  --tea-linear-gradient-fixed-1: var(--tea-color-palette-white-5);
  --tea-linear-gradient-fixed-2: var(--tea-color-palette-white-0);
  --tea-typography-display-sm-font-size: var(--tea-font-size-900);
  --tea-typography-display-sm-font-family: var(--tea-font-family-default);
  --tea-typography-display-sm-line-height: var(--tea-font-line-height-1100);
  --tea-typography-display-sm-font-weight: var(--tea-font-weight-medium);
  --tea-typography-display-md-font-size: var(--tea-font-size-1050);
  --tea-typography-display-md-font-family: var(--tea-font-family-default);
  --tea-typography-display-md-font-weight: var(--tea-font-weight-medium);
  --tea-typography-display-md-line-height: var(--tea-font-line-height-1350);
  --tea-typography-heading-1-font-size: var(--tea-font-size-800);
  --tea-typography-heading-1-font-family: var(--tea-font-family-default);
  --tea-typography-heading-1-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-1-line-height: var(--tea-font-line-height-1000);
  --tea-typography-heading-2-font-size: var(--tea-font-size-700);
  --tea-typography-heading-2-font-family: var(--tea-font-family-default);
  --tea-typography-heading-2-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-2-line-height: var(--tea-font-line-height-900);
  --tea-typography-heading-3-font-size: var(--tea-font-size-600);
  --tea-typography-heading-3-font-family: var(--tea-font-family-default);
  --tea-typography-heading-3-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-3-line-height: var(--tea-font-line-height-800);
  --tea-typography-heading-4-font-size: var(--tea-font-size-500);
  --tea-typography-heading-4-font-family: var(--tea-font-family-default);
  --tea-typography-heading-4-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-4-line-height: var(--tea-font-line-height-700);
  --tea-typography-heading-5-font-size: var(--tea-font-size-450);
  --tea-typography-heading-5-font-family: var(--tea-font-family-default);
  --tea-typography-heading-5-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-5-line-height: var(--tea-font-line-height-650);
  --tea-typography-heading-6-font-size: var(--tea-font-size-400);
  --tea-typography-heading-6-font-family: var(--tea-font-family-default);
  --tea-typography-heading-6-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-6-line-height: var(--tea-font-line-height-600);
  --tea-typography-heading-7-font-size: var(--tea-font-size-350);
  --tea-typography-heading-7-font-family: var(--tea-font-family-default);
  --tea-typography-heading-7-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-7-line-height: var(--tea-font-line-height-550);
  --tea-typography-heading-8-font-size: var(--tea-font-size-300);
  --tea-typography-heading-8-font-family: var(--tea-font-family-default);
  --tea-typography-heading-8-font-weight: var(--tea-font-weight-medium);
  --tea-typography-heading-8-line-height: var(--tea-font-line-height-500);
  --tea-typography-body-sm-font-size: var(--tea-font-size-300);
  --tea-typography-body-sm-font-family: var(--tea-font-family-default);
  --tea-typography-body-sm-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-sm-line-height: var(--tea-font-line-height-500);
  --tea-typography-body-md-font-size: var(--tea-font-size-350);
  --tea-typography-body-md-font-family: var(--tea-font-family-default);
  --tea-typography-body-md-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-md-line-height: var(--tea-font-line-height-550);
  --tea-typography-body-lg-font-size: var(--tea-font-size-400);
  --tea-typography-body-lg-font-family: var(--tea-font-family-default);
  --tea-typography-body-lg-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-lg-line-height: var(--tea-font-line-height-600);
  --tea-typography-body-xl-font-size: var(--tea-font-size-450);
  --tea-typography-body-xl-font-family: var(--tea-font-family-default);
  --tea-typography-body-xl-font-weight: var(--tea-font-weight-regular);
  --tea-typography-body-xl-line-height: var(--tea-font-line-height-650);
  --tea-size-blur-0: 0px;
  --tea-size-blur-50: 2px;
  --tea-size-blur-100: 4px;
  --tea-size-blur-200: 8px;
  --tea-size-blur-300: 12px;
  --tea-size-blur-400: 16px;
  --tea-size-blur-500: 20px;
  --tea-size-blur-600: 24px;
  --tea-size-blur-700: 28px;
  --tea-size-blur-800: 32px;
  --tea-size-blur-900: 36px;
  --tea-size-blur-1000: 40px;
  --tea-size-blur-1100: 44px;
  --tea-size-blur-1200: 48px;
  --tea-size-blur-1300: 52px;
  --tea-size-blur-1400: 56px;
  --tea-size-blur-1500: 60px;
  --tea-size-blur-1600: 64px;
  --tea-size-spread-0: 0px;
  --tea-size-spread-25: 1px;
  --tea-size-spread-50: 2px;
  --tea-size-spread-100: 4px;
  --tea-size-spread-200: 8px;
  --tea-size-spread-300: 12px;
  --tea-size-spread-400: 16px;
  --tea-size-spread-500: 20px;
  --tea-size-spread-600: 24px;
  --tea-size-spread-700: 28px;
  --tea-size-spread-800: 32px;
  --tea-size-spread-900: 36px;
  --tea-size-spread-1000: 40px;
  --tea-size-spread-1100: 44px;
  --tea-size-spread-1200: 48px;
  --tea-size-spread-0400: -16px;
  --tea-size-spread-0300: -12px;
  --tea-size-spread-0200: -8px;
  --tea-size-spread-0100: -4px;
  --tea-size-spread-050: -2px;
  --tea-size-spread-025: -1px;
  --tea-size-offset-0: 0px;
  --tea-size-offset-25: 1px;
  --tea-size-offset-50: 2px;
  --tea-size-offset-100: 4px;
  --tea-size-offset-200: 8px;
  --tea-size-offset-300: 12px;
  --tea-size-offset-400: 16px;
  --tea-size-offset-500: 20px;
  --tea-size-offset-600: 24px;
  --tea-font-size-300: 12px;
  --tea-font-size-325: 13px;
  --tea-font-size-350: 14px;
  --tea-font-size-400: 16px;
  --tea-font-size-450: 18px;
  --tea-font-size-500: 20px;
  --tea-font-size-600: 24px;
  --tea-font-size-700: 28px;
  --tea-font-size-800: 32px;
  --tea-font-size-900: 36px;
  --tea-font-size-950: 38px;
  --tea-font-size-1000: 40px;
  --tea-font-size-1050: 42px;
  --tea-font-size-default: var(--tea-font-size-300);
  --tea-font-family-default: -apple-system,BlinkMacSystemFont,'pingfang SC','Hiragina Sans GB','Helvetica Neue',Helvetica,'microsoft yahei ui','microsoft yahei',simsun,arial,sans-serif;
  --tea-font-family-code: Consolas,Menlo,Monaco,Andale Mono,Ubuntu Mono,monospace;
  --tea-font-weight-regular: 400;
  --tea-font-weight-medium: 600;
  --tea-font-line-height-300: 12px;
  --tea-font-line-height-350: 14px;
  --tea-font-line-height-400: 16px;
  --tea-font-line-height-450: 18px;
  --tea-font-line-height-500: 20px;
  --tea-font-line-height-550: 22px;
  --tea-font-line-height-600: 24px;
  --tea-font-line-height-650: 26px;
  --tea-font-line-height-700: 28px;
  --tea-font-line-height-800: 32px;
  --tea-font-line-height-850: 34px;
  --tea-font-line-height-900: 36px;
  --tea-font-line-height-950: 38px;
  --tea-font-line-height-1000: 40px;
  --tea-font-line-height-1050: 42px;
  --tea-font-line-height-1100: 44px;
  --tea-font-line-height-1150: 46px;
  --tea-font-line-height-1200: 48px;
  --tea-font-line-height-1250: 50px;
  --tea-font-line-height-1300: 52px;
  --tea-font-line-height-1350: 54px;
  --tea-font-line-height-default: var(--tea-font-line-height-500);
  --tea-border-radius-0: 0px;
  --tea-border-radius-50: 2px;
  --tea-border-radius-100: 4px;
  --tea-border-radius-150: 6px;
  --tea-border-radius-200: 8px;
  --tea-border-radius-300: 12px;
  --tea-border-radius-400: 16px;
  --tea-border-radius-500: 20px;
  --tea-border-radius-750: 30px;
  --tea-border-radius-default: var(--tea-border-radius-0);
  --tea-border-radius-full: 9999px;
  --tea-border-width-0: 0px;
  --tea-border-width-25: 1px;
  --tea-border-width-50: 2px;
  --tea-border-width-100: 4px;
  --tea-border-width-default: var(--tea-border-width-25);
  --tea-shadow-xs-blur-1: var(--tea-size-blur-200);
  --tea-shadow-xs-color-1: var(--tea-color-palette-black-20);
  --tea-shadow-xs-offsetx-1: var(--tea-size-offset-50);
  --tea-shadow-xs-offsety-1: var(--tea-size-offset-100);
  --tea-shadow-xs-spread-1: var(--tea-size-spread-0);
  --tea-shadow-sm-blur-1: var(--tea-size-blur-200);
  --tea-shadow-sm-blur-2: var(--tea-size-blur-300);
  --tea-shadow-sm-color-1: var(--tea-color-palette-black-20);
  --tea-shadow-sm-color-2: var(--tea-color-palette-black-20);
  --tea-shadow-sm-offsetx-1: var(--tea-size-offset-50);
  --tea-shadow-sm-offsetx-2: var(--tea-size-offset-0);
  --tea-shadow-sm-offsety-1: var(--tea-size-offset-100);
  --tea-shadow-sm-offsety-2: var(--tea-size-offset-50);
  --tea-shadow-sm-spread-1: var(--tea-size-spread-0);
  --tea-shadow-sm-spread-2: var(--tea-size-spread-0);
  --tea-shadow-md-blur-1: var(--tea-size-blur-600);
  --tea-shadow-md-blur-2: var(--tea-size-blur-600);
  --tea-shadow-md-color-1: var(--tea-color-palette-black-20);
  --tea-shadow-md-color-2: var(--tea-color-palette-black-30);
  --tea-shadow-md-offsetx-1: var(--tea-size-offset-0);
  --tea-shadow-md-offsetx-2: var(--tea-size-offset-0);
  --tea-shadow-md-offsety-1: var(--tea-size-offset-100);
  --tea-shadow-md-offsety-2: var(--tea-size-offset-100);
  --tea-shadow-md-spread-1: var(--tea-size-spread-0200);
  --tea-shadow-md-spread-2: var(--tea-size-spread-0100);
  --tea-shadow-lg-blur-1: var(--tea-size-blur-1600);
  --tea-shadow-lg-color-1: var(--tea-color-palette-black-70);
  --tea-shadow-lg-offsetx-1: var(--tea-size-offset-50);
  --tea-shadow-lg-offsety-1: var(--tea-size-offset-200);
  --tea-shadow-lg-spread-1: var(--tea-size-spread-0300);
  --tea-shadow-xl-blur-1: var(--tea-size-blur-1200);
  --tea-shadow-xl-color-1: var(--tea-color-palette-black-50);
  --tea-shadow-xl-offsetx-1: var(--tea-size-offset-0);
  --tea-shadow-xl-offsety-1: var(--tea-size-offset-200);
  --tea-shadow-xl-spread-1: var(--tea-size-spread-0300);
  --tea-form-height-default: 30px;
  --tea-form-color-bg-primary-default: var(--tea-color-bg-primary-default);
  --tea-form-color-bg-primary-hover: var(--tea-color-bg-primary-hover);
  --tea-form-color-bg-primary-active: var(--tea-color-bg-primary-active);
  --tea-form-color-bg-primary-focus: var(--tea-color-bg-primary-focus);
  --tea-form-color-bg-primary-disabled: var(--tea-color-bg-primary-disabled);
  --tea-form-color-border-primary-default: var(--tea-color-border-primary-default);
  --tea-form-color-border-primary-hover: var(--tea-color-border-primary-hover);
  --tea-form-color-border-primary-focus: var(--tea-color-border-primary-focus);
  --tea-form-color-border-primary-active: var(--tea-color-border-primary-active);
  --tea-form-color-border-primary-disabled: var(--tea-color-border-primary-disabled);
  --tea-form-color-text-solid: var(--tea-color-palette-white-100);
  --tea-form-color-text-primary: var(--tea-color-palette-white-90);
  --tea-form-color-text-secondary: var(--tea-color-palette-white-55);
  --tea-form-color-text-tertiary: var(--tea-color-palette-white-35);
  --tea-form-color-text-disabled: var(--tea-color-palette-white-20);
  --tea-space-0: 0px;
  --tea-space-100: 4px;
  --tea-space-200: 8px;
  --tea-space-300: 12px;
  --tea-space-400: 16px;
  --tea-space-500: 20px;
  --tea-space-600: 24px;
  --tea-space-700: 28px;
  --tea-space-800: 32px;
  --tea-space-900: 36px;
  --tea-space-1000: 40px;
  --tea-space-1100: 44px;
  --tea-space-1200: 48px;
  --tea-space-1300: 52px;
  --tea-space-1400: 56px;
  --tea-space-1500: 60px;
  --tea-space-1600: 64px;
  --tea-shadow-xs: var(--tea-shadow-xs-offsetx-1) var(--tea-shadow-xs-offsety-1) var(--tea-shadow-xs-blur-1) var(--tea-shadow-xs-spread-1) var(--tea-shadow-xs-color-1);
  --tea-shadow-sm: var(--tea-shadow-sm-offsetx-2) var(--tea-shadow-sm-offsety-2) var(--tea-shadow-sm-blur-2) var(--tea-shadow-sm-spread-2) var(--tea-shadow-sm-color-2),var(--tea-shadow-sm-offsetx-1) var(--tea-shadow-sm-offsety-1) var(--tea-shadow-sm-blur-1) var(--tea-shadow-sm-spread-1) var(--tea-shadow-sm-color-1);
  --tea-shadow-md: var(--tea-shadow-md-offsetx-2) var(--tea-shadow-md-offsety-2) var(--tea-shadow-md-blur-2) var(--tea-shadow-md-spread-2) var(--tea-shadow-md-color-2),var(--tea-shadow-md-offsetx-1) var(--tea-shadow-md-offsety-1) var(--tea-shadow-md-blur-1) var(--tea-shadow-md-spread-1) var(--tea-shadow-md-color-1);
  --tea-shadow-lg: var(--tea-shadow-lg-offsetx-1) var(--tea-shadow-lg-offsety-1) var(--tea-shadow-lg-blur-1) var(--tea-shadow-lg-spread-1) var(--tea-shadow-lg-color-1);
  --tea-shadow-xl: var(--tea-shadow-xl-offsetx-1) var(--tea-shadow-xl-offsety-1) var(--tea-shadow-xl-blur-1) var(--tea-shadow-xl-spread-1) var(--tea-shadow-xl-color-1);
  --tea-shadow-fixed-left: linear-gradient(270deg,var(--tea-linear-gradient-fixed-1),var(--tea-linear-gradient-fixed-2));
  --tea-shadow-fixed-right: linear-gradient(270deg,var(--tea-linear-gradient-fixed-2),var(--tea-linear-gradient-fixed-1));
  --tea-shadow-fixed-bottom: linear-gradient(0deg,var(--tea-linear-gradient-fixed-2),var(--tea-linear-gradient-fixed-1));
  --tea-shadow-fixed-top: linear-gradient(0deg,var(--tea-linear-gradient-fixed-1),var(--tea-linear-gradient-fixed-2));
  --tea-typography-display-sm: normal var(--tea-typography-display-sm-font-weight) var(--tea-typography-display-sm-font-size)/var(--tea-typography-display-sm-line-height) var(--tea-typography-display-sm-font-family);
  --tea-typography-display-md: normal var(--tea-typography-display-md-font-weight) var(--tea-typography-display-md-font-size)/var(--tea-typography-display-md-line-height) var(--tea-typography-display-md-font-family);
  --tea-typography-heading-1: normal var(--tea-typography-heading-1-font-weight) var(--tea-typography-heading-1-font-size)/var(--tea-typography-heading-1-line-height) var(--tea-typography-heading-1-font-family);
  --tea-typography-heading-2: normal var(--tea-typography-heading-2-font-weight) var(--tea-typography-heading-2-font-size)/var(--tea-typography-heading-2-line-height) var(--tea-typography-heading-2-font-family);
  --tea-typography-heading-3: normal var(--tea-typography-heading-3-font-weight) var(--tea-typography-heading-3-font-size)/var(--tea-typography-heading-3-line-height) var(--tea-typography-heading-3-font-family);
  --tea-typography-heading-4: normal var(--tea-typography-heading-4-font-weight) var(--tea-typography-heading-4-font-size)/var(--tea-typography-heading-4-line-height) var(--tea-typography-heading-4-font-family);
  --tea-typography-heading-5: normal var(--tea-typography-heading-5-font-weight) var(--tea-typography-heading-5-font-size)/var(--tea-typography-heading-5-line-height) var(--tea-typography-heading-5-font-family);
  --tea-typography-heading-6: normal var(--tea-typography-heading-6-font-weight) var(--tea-typography-heading-6-font-size)/var(--tea-typography-heading-6-line-height) var(--tea-typography-heading-6-font-family);
  --tea-typography-heading-7: normal var(--tea-typography-heading-7-font-weight) var(--tea-typography-heading-7-font-size)/var(--tea-typography-heading-7-line-height) var(--tea-typography-heading-7-font-family);
  --tea-typography-heading-8: normal var(--tea-typography-heading-8-font-weight) var(--tea-typography-heading-8-font-size)/var(--tea-typography-heading-8-line-height) var(--tea-typography-heading-8-font-family);
  --tea-typography-body-sm: normal var(--tea-typography-body-sm-font-weight) var(--tea-typography-body-sm-font-size)/var(--tea-typography-body-sm-line-height) var(--tea-typography-body-sm-font-family);
  --tea-typography-body-md: normal var(--tea-typography-body-md-font-weight) var(--tea-typography-body-md-font-size)/var(--tea-typography-body-md-line-height) var(--tea-typography-body-md-font-family);
  --tea-typography-body-lg: normal var(--tea-typography-body-lg-font-weight) var(--tea-typography-body-lg-font-size)/var(--tea-typography-body-lg-line-height) var(--tea-typography-body-lg-font-family);
  --tea-typography-body-xl: normal var(--tea-typography-body-xl-font-weight) var(--tea-typography-body-xl-font-size)/var(--tea-typography-body-xl-line-height) var(--tea-typography-body-xl-font-family);
  --tea-linear-gradient-fixed-right: var(--tea-shadow-fixed-right);
  --tea-linear-gradient-fixed-left: var(--tea-shadow-fixed-left);
  --tea-white: var(--tea-color-palette-white-100);
  --tea-black: var(--tea-color-palette-black-100);
  --tea-color-palette-white-1: var(--tea-color-palette-white-100);
  --tea-color-palette-white-2: var(--tea-color-palette-white-90);
  --tea-color-palette-white-3: var(--tea-color-palette-white-55);
  --tea-color-palette-white-4: var(--tea-color-palette-white-35);
  --tea-color-palette-black-1: var(--tea-color-palette-black-100);
  --tea-color-palette-black-2: var(--tea-color-palette-black-90);
  --tea-color-palette-black-3: var(--tea-color-palette-black-70);
  --tea-color-palette-black-4: var(--tea-color-palette-black-50);
  --tea-color-function-brand-default: var(--tea-color-palette-blue-6);
  --tea-color-function-brand-bright: var(--tea-color-palette-blue-2);
  --tea-color-function-brand-darkness: var(--tea-color-palette-blue-10);
  --tea-color-function-brand-hover: var(--tea-color-palette-blue-5);
  --tea-color-function-brand-focus: var(--tea-color-palette-blue-5);
  --tea-color-function-brand-active: var(--tea-color-palette-blue-9);
  --tea-color-function-brand-disabled: var(--tea-color-palette-blue-3);
  --tea-color-function-warning-default: var(--tea-color-palette-orange-6);
  --tea-color-function-warning-bright: var(--tea-color-palette-orange-2);
  --tea-color-function-warning-darkness: var(--tea-color-palette-orange-10);
  --tea-color-function-warning-hover: var(--tea-color-palette-orange-5);
  --tea-color-function-warning-focus: var(--tea-color-palette-orange-5);
  --tea-color-function-warning-active: var(--tea-color-palette-orange-9);
  --tea-color-function-warning-disabled: var(--tea-color-palette-orange-3);
  --tea-color-function-error-default: var(--tea-color-palette-red-6);
  --tea-color-function-error-bright: var(--tea-color-palette-red-2);
  --tea-color-function-error-darkness: var(--tea-color-palette-red-10);
  --tea-color-function-error-hover: var(--tea-color-palette-red-5);
  --tea-color-function-error-focus: var(--tea-color-palette-red-5);
  --tea-color-function-error-active: var(--tea-color-palette-red-9);
  --tea-color-function-error-disabled: var(--tea-color-palette-red-3);
  --tea-color-function-success-default: var(--tea-color-palette-green-6);
  --tea-color-function-success-bright: var(--tea-color-palette-green-2);
  --tea-color-function-success-darkness: var(--tea-color-palette-green-10);
  --tea-color-function-success-hover: var(--tea-color-palette-green-5);
  --tea-color-function-success-focus: var(--tea-color-palette-green-5);
  --tea-color-function-success-active: var(--tea-color-palette-green-9);
  --tea-color-function-success-disabled: var(--tea-color-palette-green-3);
  --tea-color-text-placeholder: var(--tea-color-text-tertiary);
  --tea-color-text-inverse: var(--tea-color-palette-black-90);
  --tea-color-text-brand: var(--tea-color-text-brand-default);
  --tea-color-text-success: var(--tea-color-text-success-default);
  --tea-color-text-error: var(--tea-color-text-error-default);
  --tea-color-text-warning: var(--tea-color-text-warning-default);
  --tea-color-text-highlight-solid: var(--tea-color-palette-black-100);
  --tea-color-text-highlight-primary: var(--tea-color-palette-black-90);
  --tea-color-text-highlight-secondary: var(--tea-color-palette-black-70);
  --tea-color-text-highlight-placeholder: var(--tea-color-palette-black-50);
  --tea-color-text-highlight-disabled: var(--tea-color-palette-black-30);
  --tea-color-text-highlight-inverse-solid: var(--tea-color-palette-white-100);
  --tea-color-text-highlight-inverse-primary: var(--tea-color-palette-white-90);
  --tea-color-text-highlight-inverse-secondary: var(--tea-color-palette-white-55);
  --tea-color-text-highlight-inverse-placeholder: var(--tea-color-palette-white-35);
  --tea-color-text-highlight-inverse-disabled: var(--tea-color-palette-white-20);
  --tea-color-border-primary: var(--tea-color-border-primary-default);
  --tea-color-border-secondary: var(--tea-color-border-secondary-default);
  --tea-color-border-hover: var(--tea-color-border-primary-hover);
  --tea-color-border-disabled: var(--tea-color-border-primary-disabled);
  --tea-color-border-active: var(--tea-color-border-primary-active);
  --tea-color-bg-container-default: var(--tea-color-bg-primary-default);
  --tea-color-bg-container-hover: var(--tea-color-bg-primary-hover);
  --tea-color-bg-container-active: var(--tea-color-bg-primary-active);
  --tea-color-bg-container-inverse-default: var(--tea-color-palette-white-100);
  --tea-color-bg-secondarycontainer-default: var(--tea-color-bg-secondary-default);
  --tea-color-bg-secondarycontainer-hover: var(--tea-color-bg-secondary-hover);
  --tea-color-bg-secondarycontainer-active: var(--tea-color-bg-secondary-active);
  --tea-color-bg-mask-default: var(--tea-color-mask-primary);
  --tea-color-bg-mask-secondary: var(--tea-color-mask-secondary);
  --tea-color-scrollbar-default: var(--tea-color-palette-white-20);
  --tea-color-scrollbar-hover: var(--tea-color-palette-white-40);
  --tea-color-scrolltrack: transparent;
  --tea-shadow-default: var(--tea-shadow-sm);
  --tea-color-bg-page: var(--tea-color-bg-page-default);
  --tea-shadow-dropdown: var(--tea-shadow-sm);
  --tea-color-bg-form-default: var(--tea-form-color-bg-primary-default);
  --tea-color-bg-form-hover: var(--tea-form-color-bg-primary-hover);
  --tea-color-bg-form-focus: var(--tea-form-color-bg-primary-focus);
  --tea-color-bg-form-disabled: var(--tea-form-color-bg-primary-disabled);
  --tea-color-text-form-default: var(--tea-form-color-text-primary);
  --tea-color-text-form-label: var(--tea-form-color-text-tertiary);
  --tea-color-text-form-hover: var(--tea-form-color-text-primary);
  --tea-color-text-form-focus: var(--tea-form-color-text-primary);
  --tea-color-text-form-disabled: var(--tea-form-color-text-disabled);
  --tea-color-text-form-placeholder: var(--tea-form-color-text-tertiary);
  --tea-color-border-form-default: var(--tea-form-color-border-primary-default);
  --tea-color-border-form-hover: var(--tea-form-color-border-primary-hover);
  --tea-color-border-form-focus: var(--tea-form-color-border-primary-focus);
  --tea-color-border-form-disabled: var(--tea-form-color-border-primary-disabled);
  --icon-status-blank: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMjAgNTZWMjRoNDB2MzJ6Ii8+PHBhdGggZmlsbD0iIzI2MkYzRSIgZD0iTTI2IDM3aDI4djJIMjZ6bTAgN2gyMHYySDI2em0tNi0yMGg0MHY3SDIweiIvPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDQ4IDQ0KSI+PGNpcmNsZSBmaWxsPSIjMTUxODIyIiBjeD0iMTAiIGN5PSIxMCIgcj0iNyIvPjxwYXRoIGQ9Ik0xMCAyYTggOCAwIDEgMSAwIDE2IDggOCAwIDAgMSAwLTE2em0xIDEwSDl2Mmgydi0yem0wLTZIOXY1aDJWNnoiIGZpbGw9IiMyNjg0RkYiLz48L2c+PC9nPjwvc3ZnPg==);
  --icon-status-blank-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMTQgMzkuMlYxNi44aDI4djIyLjR6Ii8+PHBhdGggZmlsbD0iIzI2MkYzRSIgZD0iTTE4LjIgMjUuOWgxOS42djEuNEgxOC4yem0wIDQuOWgxNHYxLjRoLTE0em0tNC4yLTE0aDI4djQuOUgxNHoiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgzMy42IDMwLjgpIj48Y2lyY2xlIGZpbGw9IiMxNTE4MjIiIGN4PSI3IiBjeT0iNyIgcj0iNC45Ii8+PHBhdGggZD0iTTcgMS40YTUuNiA1LjYgMCAxIDEgMCAxMS4yQTUuNiA1LjYgMCAwIDEgNyAxLjR6bS43IDdINi4zdjEuNGgxLjRWOC40em0wLTQuMkg2LjN2My41aDEuNFY0LjJ6IiBmaWxsPSIjMjY4NEZGIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-chart: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMjIgNTdWNDNoMTJ2MTR6Ii8+PHBhdGggZmlsbD0iIzI2MkYzRSIgZD0iTTM0IDU3VjMzaDEydjI0eiIvPjxwYXRoIHN0cm9rZT0iIzI2MkYzRSIgc3Ryb2tlLXdpZHRoPSIyIiBkPSJNMTggMzguNzQ3bDEyLTEyaDdMNDMuNzQ3IDIwIi8+PHBhdGggZmlsbD0iIzRFNjA4MCIgZD0iTTQ2IDU3VjIxaDEydjM2eiIvPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDQ4IDQ0KSI+PGNpcmNsZSBmaWxsPSIjMTUxODIyIiBjeD0iMTAiIGN5PSIxMCIgcj0iNyIvPjxwYXRoIGQ9Ik0xMCAyYTggOCAwIDEgMSAwIDE2IDggOCAwIDAgMSAwLTE2em0xIDEwSDl2Mmgydi0yem0wLTZIOXY1aDJWNnoiIGZpbGw9IiMyNjg0RkYiLz48L2c+PC9nPjwvc3ZnPg==);
  --icon-status-chart-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMTUuNCAzOS45di05LjhoOC40djkuOHoiLz48cGF0aCBmaWxsPSIjMjYyRjNFIiBkPSJNMjMuOCAzOS45VjIzLjFoOC40djE2Ljh6Ii8+PHBhdGggc3Ryb2tlPSIjMjYyRjNFIiBzdHJva2Utd2lkdGg9IjEuNCIgZD0iTTEyLjYgMjcuMTIzbDguNC04LjRoNC45TDMwLjYyMyAxNCIvPjxwYXRoIGZpbGw9IiM0RTYwODAiIGQ9Ik0zMi4yIDM5LjlWMTQuN2g4LjR2MjUuMnoiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgzMy42IDMwLjgpIj48Y2lyY2xlIGZpbGw9IiMxNTE4MjIiIGN4PSI3IiBjeT0iNyIgcj0iNC45Ii8+PHBhdGggZD0iTTcgMS40YTUuNiA1LjYgMCAxIDEgMCAxMS4yQTUuNiA1LjYgMCAwIDEgNyAxLjR6bS43IDdINi4zdjEuNGgxLjRWOC40em0wLTQuMkg2LjN2My41aDEuNFY0LjJ6IiBmaWxsPSIjMjY4NEZGIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-loading: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNNDAgMzYuODc3TDYwLjk5NCA0OSA0MCA2MS4xMjIgMTkuMDAzIDQ5eiIvPjxwYXRoIGZpbGw9IiMyNjJGM0UiIGQ9Ik00MCAyNy44NzdMNjAuOTk0IDQwIDQwIDUyLjEyMSAxOS4wMDMgNDB6Ii8+PHBhdGggZmlsbD0iIzRFNjA4MCIgZD0iTTQwIDE4Ljg3OEw2MC45OTQgMzEgNDAgNDMuMTIzIDE5LjAwMyAzMXoiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg0OCA0NCkiPjxjaXJjbGUgZmlsbD0iIzE1MTgyMiIgY3g9IjEwIiBjeT0iMTAiIHI9IjciLz48cGF0aCBkPSJNMTAgMmE4IDggMCAxIDEgMCAxNiA4IDggMCAwIDEgMC0xNnptMSAzSDl2NS45MTRsMy4yOTMgMy4yOTMgMS40MTQtMS40MTRMMTEgMTAuMDg1VjV6IiBmaWxsPSIjMjY4NEZGIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-loading-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMjggMjUuODE0TDQyLjY5NiAzNC4zIDI4IDQyLjc4NiAxMy4zMDIgMzQuM3oiLz48cGF0aCBmaWxsPSIjMjYyRjNFIiBkPSJNMjggMTkuNTE0TDQyLjY5NiAyOCAyOCAzNi40ODYgMTMuMzAyIDI4eiIvPjxwYXRoIGZpbGw9IiM0RTYwODAiIGQ9Ik0yOCAxMy4yMTRMNDIuNjk2IDIxLjcgMjggMzAuMTg2IDEzLjMwMiAyMS43eiIvPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDMzLjYgMzAuOCkiPjxjaXJjbGUgZmlsbD0iIzE1MTgyMiIgY3g9IjciIGN5PSI3IiByPSI0LjkiLz48cGF0aCBkPSJNNyAxLjRhNS42IDUuNiAwIDEgMSAwIDExLjJBNS42IDUuNiAwIDAgMSA3IDEuNHptLjcgMi4xSDYuM3Y0LjE0bDIuMzA1IDIuMzA1Ljk5LS45OUw3LjcgNy4wNTlWMy41eiIgZmlsbD0iIzI2ODRGRiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-network: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxNiAxNikiPjxjaXJjbGUgZmlsbD0iIzI2MkYzRSIgY3g9IjI0IiBjeT0iMjQiIHI9IjIyIi8+PHBhdGggZD0iTTI0IDJoLS4xMDNjMi40ODYgMCA0Ljg3NS40MTIgNy4xMDQgMS4xNzJMMzEgMTJoLTd2OWgtOHY2aDE0bDUgNWg2bC4wMDEgNS44MzhjLTMuOTQ3IDQuODczLTkuOTM1IDguMDI2LTE2LjY2NCA4LjE1OGwtLjMzNy4wMDNWMzZoLTZ2LTRMMy4xMDMgMTcuMTAyQzUuOTk3IDguMzMxIDE0LjI2IDIgMjQgMnoiIGZpbGw9IiM0RTYwODAiLz48L2c+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNDggNDQpIj48Y2lyY2xlIGZpbGw9IiMxNTE4MjIiIGN4PSIxMCIgY3k9IjEwIiByPSI3Ii8+PHBhdGggZD0iTTEwIDJhOCA4IDAgMSAxIDAgMTYgOCA4IDAgMCAxIDAtMTZ6bTEgMTBIOXYyaDJ2LTJ6bTAtNkg5djVoMlY2eiIgZmlsbD0iIzI2ODRGRiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-network-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMS4yIDExLjIpIj48Y2lyY2xlIGZpbGw9IiMyNjJGM0UiIGN4PSIxNi44IiBjeT0iMTYuOCIgcj0iMTUuNCIvPjxwYXRoIGQ9Ik0xNi44IDEuNGgtLjA3MmMxLjc0IDAgMy40MTMuMjg5IDQuOTcyLjgyVjguNGgtNC45djYuM2gtNS42djQuMkgyMWwzLjUgMy41aDQuMmwuMDAxIDQuMDg2YTE1LjM3IDE1LjM3IDAgMCAxLTExLjY2NSA1LjcxMWwtLjIzNi4wMDJWMjUuMmgtNC4ydi0yLjhMMi4xNzIgMTEuOTcxQzQuMTk4IDUuODMxIDkuOTgyIDEuNCAxNi44IDEuNHoiIGZpbGw9IiM0RTYwODAiLz48L2c+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMzMuNiAzMC44KSI+PGNpcmNsZSBmaWxsPSIjMTUxODIyIiBjeD0iNyIgY3k9IjciIHI9IjQuOSIvPjxwYXRoIGQ9Ik03IDEuNGE1LjYgNS42IDAgMSAxIDAgMTEuMkE1LjYgNS42IDAgMCAxIDcgMS40em0uNyA3SDYuM3YxLjRoMS40VjguNHptMC00LjJINi4zdjMuNWgxLjRWNC4yeiIgZmlsbD0iIzI2ODRGRiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-no-permission: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48cGF0aCBmaWxsPSIjMjYyRjNFIiBkPSJNNDAgMzYuODc3bDIwLjk5OSAxMi4xMjV2LTE5TDQwIDE3Ljg3OCAxOC45OTkgMzAuMDAydjE5eiIvPjxwYXRoIGZpbGw9IiM0RTYwODAiIGQ9Ik00MCA0Mi4xMjNsMjAuOTk5LTEyLjEyNXYyMEw0MCA2Mi4xMjMgMTguOTk5IDQ5Ljk5OHYtMjB6Ii8+PHBhdGggZD0iTTU4IDQ1YTUgNSAwIDAgMSA1IDV2MWgydjExSDUxVjUxaDJ2LTFhNSA1IDAgMCAxIDUtNXptMCAyYTMgMyAwIDAgMC0yLjk5NSAyLjgyNEw1NSA1MHYxaDZ2LTFhMyAzIDAgMCAwLTIuODI0LTIuOTk1TDU4IDQ3eiIgZmlsbD0iIzI2ODRGRiIvPjxwYXRoIGZpbGw9IiMxNTE4MjIiIGQ9Ik01NyA1NGgydjVoLTJ6Ii8+PC9nPjwvc3ZnPg==);
  --icon-status-no-permission-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjMjYyRjNFIiBkPSJNMjggMjUuODE0bDE0LjcgOC40ODh2LTEzLjNMMjggMTIuNTE0bC0xNC43IDguNDg4djEzLjN6Ii8+PHBhdGggZmlsbD0iIzRFNjA4MCIgZD0iTTI4IDI5LjQ4NmwxNC43LTguNDg4djE0TDI4IDQzLjQ4NmwtMTQuNy04LjQ4OHYtMTR6Ii8+PHBhdGggZD0iTTQwLjYgMzEuNWEzLjUgMy41IDAgMCAxIDMuNSAzLjV2LjY5OWwxLjQuMDAxdjcuN2gtOS44di03LjdsMS40LS4wMDFWMzVhMy41IDMuNSAwIDAgMSAzLjUtMy41em0wIDEuNGEyLjEgMi4xIDAgMCAwLTIuMDk2IDEuOTc3TDM4LjUgMzV2LjdoNC4yVjM1YTIuMSAyLjEgMCAwIDAtMS45NzctMi4wOTZMNDAuNiAzMi45eiIgZmlsbD0iIzI2ODRGRiIvPjxwYXRoIGZpbGw9IiMxNTE4MjIiIGQ9Ik0zOS45IDM3LjhoMS40djMuNWgtMS40eiIvPjwvZz48L3N2Zz4=);
  --icon-status-pay: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMTkgNTVWMjVoNDJ2MzB6Ii8+PHBhdGggZmlsbD0iIzI2MkYzRSIgZD0iTTQ4IDQyaDl2OWgtOXpNMTkgMzBoNDJ2OEgxOXoiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg0OCA0NCkiPjxjaXJjbGUgZmlsbD0iIzE1MTgyMiIgY3g9IjEwIiBjeT0iMTAiIHI9IjciLz48cGF0aCBkPSJNMTAgMmE4IDggMCAxIDEgMCAxNiA4IDggMCAwIDEgMC0xNnptMSAxMEg5djJoMnYtMnptMC02SDl2NWgyVjZ6IiBmaWxsPSIjMjY4NEZGIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-pay-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMTMuMyAzOC41di0yMWgyOS40djIxeiIvPjxwYXRoIGZpbGw9IiMyNjJGM0UiIGQ9Ik0zMy42IDI5LjRoNi4zdjYuM2gtNi4zek0xMy4zIDIxaDI5LjR2NS42SDEzLjN6Ii8+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMzMuNiAzMC44KSI+PGNpcmNsZSBmaWxsPSIjMTUxODIyIiBjeD0iNyIgY3k9IjciIHI9IjQuOSIvPjxwYXRoIGQ9Ik03IDEuNGE1LjYgNS42IDAgMSAxIDAgMTEuMkE1LjYgNS42IDAgMCAxIDcgMS40em0uNyA3SDYuM3YxLjRoMS40VjguNHptMC00LjJINi4zdjMuNWgxLjRWNC4yeiIgZmlsbD0iIzI2ODRGRiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-search: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48cGF0aCBmaWxsPSIjMjYyRjNFIiBkPSJNMTkgMjNoMTVsNCA0aDE5djMwSDE5eiIvPjxwYXRoIGQ9Ik02My40MzEgMzNMNTcgNTdIMTlsNi40MzEtMjRoMzh6IiBmaWxsPSIjNEU2MDgwIi8+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNDggNDQpIj48Y2lyY2xlIGZpbGw9IiNGRkYiIGN4PSIxMCIgY3k9IjEwIiByPSI3Ii8+PHBhdGggZD0iTTEwIDJhOCA4IDAgMSAwIDAgMTYgOCA4IDAgMCAwIDAtMTZ6IiBmaWxsPSIjMjY4NEZGIi8+PHBhdGggZD0iTTkuMjUyIDVhNC4yNTIgNC4yNTIgMCAwIDEgMy42MyA2LjQ2N2wxLjc3NSAxLjc3Ni0xLjQxNCAxLjQxNC0xLjc3Ni0xLjc3NkE0LjI1MiA0LjI1MiAwIDEgMSA5LjI1MSA1em0wIDJhMi4yNTIgMi4yNTIgMCAxIDAgMCA0LjUwNCAyLjI1MiAyLjI1MiAwIDAgMCAwLTQuNTA0eiIgZmlsbD0iIzE1MTgyMiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-search-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjMjYyRjNFIiBkPSJNMTMuMyAxNi4xaDEwLjVsMi44IDIuOGgxMy4zdjIxSDEzLjN6Ii8+PHBhdGggZD0iTTQ0LjQwMiAyMy4xTDM5LjkgMzkuOUgxMy4zbDQuNTAyLTE2LjhoMjYuNnoiIGZpbGw9IiM0RTYwODAiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgzMy42IDMwLjgpIj48Y2lyY2xlIGZpbGw9IiNGRkYiIGN4PSI3IiBjeT0iNyIgcj0iNC45Ii8+PHBhdGggZD0iTTcgMS40YTUuNiA1LjYgMCAxIDAgMCAxMS4yQTUuNiA1LjYgMCAwIDAgNyAxLjR6IiBmaWxsPSIjMjY4NEZGIi8+PHBhdGggZD0iTTYuNDc2IDMuNWEyLjk3NiAyLjk3NiAwIDAgMSAyLjU0MSA0LjUyN0wxMC4yNiA5LjI3bC0uOTkuOTktMS4yNDMtMS4yNDNBMi45NzYgMi45NzYgMCAxIDEgNi40NzcgMy41em0wIDEuNGExLjU3NiAxLjU3NiAwIDEgMCAwIDMuMTUzIDEuNTc2IDEuNTc2IDAgMCAwIDAtMy4xNTN6IiBmaWxsPSIjMTUxODIyIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-upload: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSI0MCIgZmlsbD0iIzE1MTgyMiIgb3BhY2l0eT0iLjUiLz48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMjAgNTZWMjRoNDB2MzJ6Ii8+PHBhdGggZD0iTTMxIDMyLjEzbDE4IDE4IDEuNDE0LTEuNDE0LTUtNUw1MSAzOC4xM2w5IDlWNTZIMjBWNDMuMTI5bDExLTExeiIgZmlsbD0iIzI2MkYzRSIvPjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDQ4IDQ0KSI+PGNpcmNsZSBmaWxsPSIjMTUxODIyIiBjeD0iMTAiIGN5PSIxMCIgcj0iNyIvPjxwYXRoIGQ9Ik0xNCAxMWgtM3YzSDl2LTNINlY5aDNWNmgydjNoM3Yyek0yIDEwYTggOCAwIDEgMCAxNiAwIDggOCAwIDAgMC0xNiAweiIgZmlsbD0iIzI2ODRGRiIvPjwvZz48L2c+PC9zdmc+);
  --icon-status-upload-s: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjNEU2MDgwIiBkPSJNMTQgMzkuMlYxNi44aDI4djIyLjR6Ii8+PHBhdGggZD0iTTIxLjcgMjIuNDkxbDEyLjYgMTIuNi45OS0uOTktMy41MDEtMy41IDMuOTExLTMuOTEgNi4zIDYuM1YzOS4ySDE0di05LjAxbDcuNy03LjY5OXoiIGZpbGw9IiMyNjJGM0UiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSgzMy42IDMwLjgpIj48Y2lyY2xlIGZpbGw9IiMxNTE4MjIiIGN4PSI3IiBjeT0iNyIgcj0iNC45Ii8+PHBhdGggZD0iTTkuOCA3LjdINy43djIuMUg2LjNWNy43SDQuMlY2LjNoMi4xVjQuMmgxLjR2Mi4xaDIuMXYxLjR6TTEuNCA3YTUuNiA1LjYgMCAxIDAgMTEuMiAwQTUuNiA1LjYgMCAwIDAgMS40IDd6IiBmaWxsPSIjMjY4NEZGIi8+PC9nPjwvZz48L3N2Zz4=);
  --icon-status-blank-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zOC45OSAyMy45MzJhMiAyIDAgMCAxIDIuNDUtMS40MTVsMTguODk5IDUuMDY0YTIgMiAwIDAgMSAxLjQxNiAyLjQ0TDU2LjUgNTAuMDI2IDQ3Ljk5OSA1NGwtMTQuMDI0LTMuNDg0YTIgMiAwIDAgMS0xLjQ1LTIuNDU4bDYuNDY1LTI0LjEyNnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjE5IC4xOSAuMTkpIi8+PHBhdGggZD0iTTQ5LjM4MiA0OS44NTRMNDggNTRsOC41LTQtNC42MzItMS40MjVhMiAyIDAgMCAwLTIuNDg2IDEuMjc5eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMyAuMyAuMykiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTIuMDc4IDE5LjA5NmEyIDIgMCAwIDEgMS40MTQtMi40NWwyNy4zMTgtNy4zMmEyIDIgMCAwIDEgMi40NSAxLjQxNGw5LjAwNSAzMy42MDhhMiAyIDAgMCAxLTEuNDE0IDIuNDVsLTI3LjMxOCA3LjMyYTIgMiAwIDAgMS0yLjQ1LTEuNDE1TDIuMDc4IDE5LjA5NnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjI0IC4yNCAuMjQpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05LjgzIDI0Ljk2M2wxOS4zMTgtNS4xNzYgMS4wMzYgMy44NjQtMTkuMzE5IDUuMTc2LTEuMDM1LTMuODY0em0xLjU1MyA1Ljc5NmwxMC40NDEtMi43OTggMS4wMzUgMy44NjQtMTAuNDQxIDIuNzk4LTEuMDM1LTMuODY0eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMzkgLjM5IC4zOSkiLz48cGF0aCBkPSJNNTggMTAuNWwuMzg3IDEuMDQ1YTMuNSAzLjUgMCAwIDAgMi4wNjggMi4wNjhMNjEuNSAxNGwtMS4wNDUuMzg3YTMuNSAzLjUgMCAwIDAtMi4wNjggMi4wNjhMNTggMTcuNWwtLjM4Ny0xLjA0NWEzLjUgMy41IDAgMCAwLTIuMDY4LTIuMDY4TDU0LjUgMTRsMS4wNDUtLjM4N2EzLjUgMy41IDAgMCAwIDIuMDY4LTIuMDY4TDU4IDEwLjV6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC43NCAuNzQgLjc0KSIvPjwvc3ZnPg==);
  --icon-status-blank-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zOC45OSAyMy45MzJhMiAyIDAgMCAxIDIuNDUtMS40MTVsMTguODk5IDUuMDY0YTIgMiAwIDAgMSAxLjQxNiAyLjQ0TDU2LjUgNTAuMDI2IDQ3Ljk5OSA1NGwtMTQuMDI0LTMuNDg0YTIgMiAwIDAgMS0xLjQ1LTIuNDU4bDYuNDY1LTI0LjEyNnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjE5IC4xOSAuMTkpIi8+PHBhdGggZD0iTTQ5LjM4MiA0OS44NTRMNDggNTRsOC41LTQtNC42MzItMS40MjVhMiAyIDAgMCAwLTIuNDg2IDEuMjc5eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMyAuMyAuMykiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTIuMDc4IDE5LjA5NmEyIDIgMCAwIDEgMS40MTQtMi40NWwyNy4zMTgtNy4zMmEyIDIgMCAwIDEgMi40NSAxLjQxNGw5LjAwNSAzMy42MDhhMiAyIDAgMCAxLTEuNDE0IDIuNDVsLTI3LjMxOCA3LjMyYTIgMiAwIDAgMS0yLjQ1LTEuNDE1TDIuMDc4IDE5LjA5NnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjI0IC4yNCAuMjQpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05LjgzIDI0Ljk2M2wxOS4zMTgtNS4xNzYgMS4wMzYgMy44NjQtMTkuMzE5IDUuMTc2LTEuMDM1LTMuODY0em0xLjU1MyA1Ljc5NmwxMC40NDEtMi43OTggMS4wMzUgMy44NjQtMTAuNDQxIDIuNzk4LTEuMDM1LTMuODY0eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMzkgLjM5IC4zOSkiLz48cGF0aCBkPSJNNTggMTAuNWwuMzg3IDEuMDQ1YTMuNSAzLjUgMCAwIDAgMi4wNjggMi4wNjhMNjEuNSAxNGwtMS4wNDUuMzg3YTMuNSAzLjUgMCAwIDAtMi4wNjggMi4wNjhMNTggMTcuNWwtLjM4Ny0xLjA0NWEzLjUgMy41IDAgMCAwLTIuMDY4LTIuMDY4TDU0LjUgMTRsMS4wNDUtLjM4N2EzLjUgMy41IDAgMCAwIDIuMDY4LTIuMDY4TDU4IDEwLjV6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC43NCAuNzQgLjc0KSIvPjwvc3ZnPg==);
  --icon-status-chart-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2Ljc3IDkuNzNsMy4zNDIgMy4zNDJTMTguNjggMzEuNTg2IDQuNSAzMS41ODZjMTUuMjMgMCAzMC4wNjgtMTQuMDU3IDMwLjA2OC0xNC4wNTdsMy4zNDIgMy4zNDIgMS45NTMtMTMuMDk0TDI2Ljc3IDkuNzN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yMyAuMjMgLjIzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEgNDlWMzRoMTF2MTVIMTF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjYgNDlWMjhoMTF2MjFIMjZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yNTEgLjI1MSAuMjUxKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNDEgNDguOTQzVjE4LjI3aDExdjMwLjY3M0g0MXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZD0iTTEwIDUzaDQ0IiBzdHJva2U9ImNvbG9yKGRpc3BsYXktcDMgLjMwOTggLjMwOTggLjMwOTgpIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz48L3N2Zz4=);
  --icon-status-chart-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2Ljc3IDkuNzNsMy4zNDIgMy4zNDJTMTguNjggMzEuNTg2IDQuNSAzMS41ODZjMTUuMjMgMCAzMC4wNjgtMTQuMDU3IDMwLjA2OC0xNC4wNTdsMy4zNDIgMy4zNDIgMS45NTMtMTMuMDk0TDI2Ljc3IDkuNzN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yMyAuMjMgLjIzKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEgNDlWMzRoMTF2MTVIMTF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjYgNDlWMjhoMTF2MjFIMjZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yNTEgLjI1MSAuMjUxKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNDEgNDguOTQzVjE4LjI3aDExdjMwLjY3M0g0MXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZD0iTTEwIDUzaDQ0IiBzdHJva2U9ImNvbG9yKGRpc3BsYXktcDMgLjMwOTggLjMwOTggLjMwOTgpIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz48L3N2Zz4=);
  --icon-status-loading-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjQiIHN0cm9rZT0iY29sb3IoZGlzcGxheS1wMyAuMjEgLjIxIC4yMSkiIHN0cm9rZS13aWR0aD0iMyIvPjxwYXRoIGQ9Ik01NiAzMkM1NiAxOC43NDUgNDUuMjU1IDggMzIgOCIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC4zOSAuMzkgLjM5KSIgc3Ryb2tlLXdpZHRoPSIzIi8+PC9zdmc+);
  --icon-status-loading-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjQiIHN0cm9rZT0iY29sb3IoZGlzcGxheS1wMyAuMjEgLjIxIC4yMSkiIHN0cm9rZS13aWR0aD0iMyIvPjxwYXRoIGQ9Ik01NiAzMkM1NiAxOC43NDUgNDUuMjU1IDggMzIgOCIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC4zOSAuMzkgLjM5KSIgc3Ryb2tlLXdpZHRoPSIzIi8+PC9zdmc+);
  --icon-status-network-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zMy44NDYgMjUuOTM4TDMyLjUgMzAuMTk2aDUuMDJsLTEuMTY2IDUuNjI3Yy01LjY0NS0xLjMyLTExLjc4NC4zNTMtMTYuMTc1IDUuMDE5bC02LjMwOC02LjcwNGM1LjQ4OS01LjgzMiAxMi43ODktOC41NjYgMTkuOTc1LTguMnptMS4zNTYtNC4yOWMtOC42OTMtLjgxNS0xNy42NTEgMi4zMTMtMjQuMzA0IDkuMzgzTDQuNSAyNC4yMzNjOS4xNzItOS43NDcgMjEuNzI0LTEzLjcxNCAzMy42NDYtMTEuOWwtMi45NDQgOS4zMTV6bTguNDY4IDYuODQzbC0zLjcxNyA4LjYyMmExNy41MTkgMTcuNTE5IDAgMCAxIDUuMDEgMy43MjdsNi4zMS02LjcwNC0uMzEyLS4zMjZhMjYuMzc3IDI2LjM3NyAwIDAgMC03LjI5MS01LjMyek0zNS4zMzYgNDAuNzNjLTQuMTIxLS45OTctOC42Mi4yMS0xMS44MyAzLjYybDguOTkzIDkuNTU2LjEzNC0uMTQzIDIuNzAzLTEzLjAzM3pNMzIuOSA1My40ODFsNS4wNzYtMTEuNzc3YTEyLjczIDEyLjczIDAgMCAxIDMuNTE3IDIuNjQ1bC04LjU5MyA5LjEzMnptOC43NjYtMzAuNDcybDMuMDU2LTkuMDU5YzUuNTc1IDEuOTE3IDEwLjgzIDUuMTggMTUuMzEyIDkuNzkybC40NjcuNDg4LTYuMzk4IDYuNzk5Yy0zLjYwNS0zLjgzMS03Ljg4Ny02LjUwNC0xMi40MzctOC4wMnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ4IiByPSI3IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zOSAuMzkgLjM5KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNDYuMjA3IDUxLjgyNGgtMS40MTJ2LTEuNDExaDEuNDEydjEuNDExek00Ni41IDQ0bC0uMjM4IDUuMDUxaC0xLjUyNEw0NC41IDQ0aDJ6IiBmaWxsPSIjZmZmIi8+PC9zdmc+);
  --icon-status-network-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zMy44NDYgMjUuOTM4TDMyLjUgMzAuMTk2aDUuMDJsLTEuMTY2IDUuNjI3Yy01LjY0NS0xLjMyLTExLjc4NC4zNTMtMTYuMTc1IDUuMDE5bC02LjMwOC02LjcwNGM1LjQ4OS01LjgzMiAxMi43ODktOC41NjYgMTkuOTc1LTguMnptMS4zNTYtNC4yOWMtOC42OTMtLjgxNS0xNy42NTEgMi4zMTMtMjQuMzA0IDkuMzgzTDQuNSAyNC4yMzNjOS4xNzItOS43NDcgMjEuNzI0LTEzLjcxNCAzMy42NDYtMTEuOWwtMi45NDQgOS4zMTV6bTguNDY4IDYuODQzbC0zLjcxNyA4LjYyMmExNy41MTkgMTcuNTE5IDAgMCAxIDUuMDEgMy43MjdsNi4zMS02LjcwNC0uMzEyLS4zMjZhMjYuMzc3IDI2LjM3NyAwIDAgMC03LjI5MS01LjMyek0zNS4zMzYgNDAuNzNjLTQuMTIxLS45OTctOC42Mi4yMS0xMS44MyAzLjYybDguOTkzIDkuNTU2LjEzNC0uMTQzIDIuNzAzLTEzLjAzM3pNMzIuOSA1My40ODFsNS4wNzYtMTEuNzc3YTEyLjczIDEyLjczIDAgMCAxIDMuNTE3IDIuNjQ1bC04LjU5MyA5LjEzMnptOC43NjYtMzAuNDcybDMuMDU2LTkuMDU5YzUuNTc1IDEuOTE3IDEwLjgzIDUuMTggMTUuMzEyIDkuNzkybC40NjcuNDg4LTYuMzk4IDYuNzk5Yy0zLjYwNS0zLjgzMS03Ljg4Ny02LjUwNC0xMi40MzctOC4wMnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PGNpcmNsZSBjeD0iNDUuNSIgY3k9IjQ4IiByPSI3IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zOSAuMzkgLjM5KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNDYuMjA3IDUxLjgyNGgtMS40MTJ2LTEuNDExaDEuNDEydjEuNDExek00Ni41IDQ0bC0uMjM4IDUuMDUxaC0xLjUyNEw0NC41IDQ0aDJ6IiBmaWxsPSIjZmZmIi8+PC9zdmc+);
  --icon-status-no-permission-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjYiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZD0iTTM4LjYyIDI2LjcxNnYtMy4wNDNhNi42NjMgNi42NjMgMCAwIDAtNi42NjQtNi42NjN2MGE2LjY2MyA2LjY2MyAwIDAgMC02LjY2MyA2LjY2M3YzLjA0MyIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC40MiAuNDIgLjQyKSIgc3Ryb2tlLXdpZHRoPSIzLjk5OCIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIvPjxwYXRoIGQ9Ik0xOS40NjUgMjguOTA4aDI0Ljk4OHYxNi42NTlIMTkuNDY1VjI4LjkwOHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjMyIC4zMiAuMzIpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zMS4yNDIgNDIuMDE0VjM3Ljg1aDEuNDI4djQuMTY0aC0xLjQyOHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjc0IC43NCAuNzQpIi8+PHBhdGggZD0iTTI5LjczNCAzNC45MjdhMi4xNDMgMi4xNDMgMCAxIDAgNC4yODYgMCAyLjE0MyAyLjE0MyAwIDAgMC00LjI4NiAweiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuNzQgLjc0IC43NCkiLz48L3N2Zz4=);
  --icon-status-no-permission-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMjYiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZD0iTTM4LjYyIDI2LjcxNnYtMy4wNDNhNi42NjMgNi42NjMgMCAwIDAtNi42NjQtNi42NjN2MGE2LjY2MyA2LjY2MyAwIDAgMC02LjY2MyA2LjY2M3YzLjA0MyIgc3Ryb2tlPSJjb2xvcihkaXNwbGF5LXAzIC40MiAuNDIgLjQyKSIgc3Ryb2tlLXdpZHRoPSIzLjk5OCIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIvPjxwYXRoIGQ9Ik0xOS40NjUgMjguOTA4aDI0Ljk4OHYxNi42NTlIMTkuNDY1VjI4LjkwOHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjMyIC4zMiAuMzIpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zMS4yNDIgNDIuMDE0VjM3Ljg1aDEuNDI4djQuMTY0aC0xLjQyOHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjc0IC43NCAuNzQpIi8+PHBhdGggZD0iTTI5LjczNCAzNC45MjdhMi4xNDMgMi4xNDMgMCAxIDAgNC4yODYgMCAyLjE0MyAyLjE0MyAwIDAgMC00LjI4NiAweiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuNzQgLjc0IC43NCkiLz48L3N2Zz4=);
  --icon-status-pay-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05Ljk2NyA0Mi4xYTEgMSAwIDAgMS0uNzA3LTEuMjI0bDguMjgyLTMwLjkxYTEgMSAwIDAgMSAxLjIyNS0uNzA3TDYwLjMgMjAuMzg4YTEgMSAwIDAgMSAuNzA4IDEuMjI1bC04LjI4MyAzMC45MWExIDEgMCAwIDEtMS4yMjQuNzA2TDkuOTY3IDQyLjF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4xOSAuMTkgLjE5KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOSA1M2ExIDEgMCAwIDEtMS0xVjIwYTEgMSAwIDAgMSAxLTFoNDNhMSAxIDAgMCAxIDEgMXYzMmExIDEgMCAwIDEtMSAxSDl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yNCAuMjQgLjI0KSIvPjxwYXRoIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkwMiAuOTAyIC45MDIpIiBkPSJNMTQgNDVoMnYyaC0yem01IDBoMnYyaC0yeiIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAzNXYtOWg0NXY5SDh6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjxwYXRoIGQ9Ik00OCA0MGgtN3Y3aDd2LTd6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjwvc3ZnPg==);
  --icon-status-pay-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik05Ljk2NyA0Mi4xYTEgMSAwIDAgMS0uNzA3LTEuMjI0bDguMjgyLTMwLjkxYTEgMSAwIDAgMSAxLjIyNS0uNzA3TDYwLjMgMjAuMzg4YTEgMSAwIDAgMSAuNzA4IDEuMjI1bC04LjI4MyAzMC45MWExIDEgMCAwIDEtMS4yMjQuNzA2TDkuOTY3IDQyLjF6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4xOSAuMTkgLjE5KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOSA1M2ExIDEgMCAwIDEtMS0xVjIwYTEgMSAwIDAgMSAxLTFoNDNhMSAxIDAgMCAxIDEgMXYzMmExIDEgMCAwIDEtMSAxSDl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yNCAuMjQgLjI0KSIvPjxwYXRoIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjkwMiAuOTAyIC45MDIpIiBkPSJNMTQgNDVoMnYyaC0yem01IDBoMnYyaC0yeiIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAzNXYtOWg0NXY5SDh6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjxwYXRoIGQ9Ik00OCA0MGgtN3Y3aDd2LTd6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjwvc3ZnPg==);
  --icon-status-search-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iMTAiIHk9IjEzIiB3aWR0aD0iNDQiIGhlaWdodD0iMTEiIHJ4PSIxIiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjxyZWN0IHg9IjEwIiB5PSIyNiIgd2lkdGg9IjExIiBoZWlnaHQ9IjI1IiByeD0iMSIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMjEgLjIxIC4yMSkiLz48cmVjdCB4PSIyMyIgeT0iMjYiIHdpZHRoPSIzMSIgaGVpZ2h0PSIyNSIgcng9IjEiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00Ni43NSA0OS41ODNhNS44MzMgNS44MzMgMCAxIDAgMC0xMS42NjYgNS44MzMgNS44MzMgMCAwIDAgMCAxMS42NjZ6bTAgMS42NjdhNy41IDcuNSAwIDEgMCAwLTE1IDcuNSA3LjUgMCAwIDAgMCAxNXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjYyIC42MiAuNjIpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik01Ni4xNiA1NC4zMzlsLTUtNSAxLjE3OS0xLjE3OSA1IDUtMS4xNzkgMS4xNzl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC42MiAuNjIgLjYyKSIvPjwvc3ZnPg==);
  --icon-status-search-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iMTAiIHk9IjEzIiB3aWR0aD0iNDQiIGhlaWdodD0iMTEiIHJ4PSIxIiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zMDk4IC4zMDk4IC4zMDk4KSIvPjxyZWN0IHg9IjEwIiB5PSIyNiIgd2lkdGg9IjExIiBoZWlnaHQ9IjI1IiByeD0iMSIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMjEgLjIxIC4yMSkiLz48cmVjdCB4PSIyMyIgeT0iMjYiIHdpZHRoPSIzMSIgaGVpZ2h0PSIyNSIgcng9IjEiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00Ni43NSA0OS41ODNhNS44MzMgNS44MzMgMCAxIDAgMC0xMS42NjYgNS44MzMgNS44MzMgMCAwIDAgMCAxMS42NjZ6bTAgMS42NjdhNy41IDcuNSAwIDEgMCAwLTE1IDcuNSA3LjUgMCAwIDAgMCAxNXoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjYyIC42MiAuNjIpIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik01Ni4xNiA1NC4zMzlsLTUtNSAxLjE3OS0xLjE3OSA1IDUtMS4xNzkgMS4xNzl6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC42MiAuNjIgLjYyKSIvPjwvc3ZnPg==);
  --icon-status-upload-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3LjQ4NiAyNS41ODZhMS4yMiAxLjIyIDAgMCAxLS44OTQtLjkzIDE2LjA2IDE2LjA2IDAgMCAwLTUuMjUzLTguOTEzIDE1Ljg2NyAxNS44NjcgMCAwIDAtMTAuMzQtMy44MzdjLTMuNzkgMC03LjQ1NSAxLjM2LTEwLjMzOCAzLjgzN2ExNi4wNiAxNi4wNiAwIDAgMC01LjI1MyA4LjkxMyAxLjIyIDEuMjIgMCAwIDEtLjg5NS45MyAxMy4yNzEgMTMuMjcxIDAgMCAwLTcuMzU3IDQuOTM1IDEzLjQzIDEzLjQzIDAgMCAwIDEuNjY4IDE3Ljg5IDEzLjIyNiAxMy4yMjYgMCAwIDAgOC45MTQgMy40OTVINDQuMjg3YTEzLjIyNyAxMy4yMjcgMCAwIDAgOC45MDMtMy41MDkgMTMuNDMgMTMuNDMgMCAwIDAgMS42NDktMTcuODgyIDEzLjI3IDEzLjI3IDAgMCAwLTcuMzU0LTQuOTN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yMSAuMjEgLjIxKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjcuMjQyIDQ2LjgxM1YzNy41aC02LjM4N2wxMC42NDYtMTMuNzNMNDIuMTQ1IDM3LjVoLTYuMzg3djkuMzEzaC04LjUxNnptOC41MTYgMS4wNTVoLTguNTE2djQuMDM4aDguNTE2di00LjAzOHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjM5IC4zOSAuMzkpIi8+PHBhdGggZD0iTTguNSAxM2wuNDQyIDEuMTk1YTQgNCAwIDAgMCAyLjM2MyAyLjM2M0wxMi41IDE3bC0xLjE5NS40NDJhNCA0IDAgMCAwLTIuMzYzIDIuMzYzTDguNSAyMWwtLjQ0Mi0xLjE5NWE0IDQgMCAwIDAtMi4zNjMtMi4zNjNMNC41IDE3bDEuMTk1LS40NDJhNCA0IDAgMCAwIDIuMzYzLTIuMzYzTDguNSAxM3oiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjYyIC42MiAuNjIpIi8+PC9zdmc+);
  --icon-status-upload-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3LjQ4NiAyNS41ODZhMS4yMiAxLjIyIDAgMCAxLS44OTQtLjkzIDE2LjA2IDE2LjA2IDAgMCAwLTUuMjUzLTguOTEzIDE1Ljg2NyAxNS44NjcgMCAwIDAtMTAuMzQtMy44MzdjLTMuNzkgMC03LjQ1NSAxLjM2LTEwLjMzOCAzLjgzN2ExNi4wNiAxNi4wNiAwIDAgMC01LjI1MyA4LjkxMyAxLjIyIDEuMjIgMCAwIDEtLjg5NS45MyAxMy4yNzEgMTMuMjcxIDAgMCAwLTcuMzU3IDQuOTM1IDEzLjQzIDEzLjQzIDAgMCAwIDEuNjY4IDE3Ljg5IDEzLjIyNiAxMy4yMjYgMCAwIDAgOC45MTQgMy40OTVINDQuMjg3YTEzLjIyNyAxMy4yMjcgMCAwIDAgOC45MDMtMy41MDkgMTMuNDMgMTMuNDMgMCAwIDAgMS42NDktMTcuODgyIDEzLjI3IDEzLjI3IDAgMCAwLTcuMzU0LTQuOTN6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yMSAuMjEgLjIxKSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjcuMjQyIDQ2LjgxM1YzNy41aC02LjM4N2wxMC42NDYtMTMuNzNMNDIuMTQ1IDM3LjVoLTYuMzg3djkuMzEzaC04LjUxNnptOC41MTYgMS4wNTVoLTguNTE2djQuMDM4aDguNTE2di00LjAzOHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjM5IC4zOSAuMzkpIi8+PHBhdGggZD0iTTguNSAxM2wuNDQyIDEuMTk1YTQgNCAwIDAgMCAyLjM2MyAyLjM2M0wxMi41IDE3bC0xLjE5NS40NDJhNCA0IDAgMCAwLTIuMzYzIDIuMzYzTDguNSAyMWwtLjQ0Mi0xLjE5NWE0IDQgMCAwIDAtMi4zNjMtMi4zNjNMNC41IDE3bDEuMTk1LS40NDJhNCA0IDAgMCAwIDIuMzYzLTIuMzYzTDguNSAxM3oiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjYyIC42MiAuNjIpIi8+PC9zdmc+);
  --icon-status-error-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0yOC4yMSAxNC44MDZMOC4yOSAxNi4yMjZhMS4wNjcgMS4wNjcgMCAwIDAtLjk4OCAxLjE0MmwyLjM1NSAzMy4wMDhjLjA0Mi41ODkuNTUyIDEuMDMxIDEuMTQuOTlsMTMuOTc1LS45OTggOS4xMjQtMTQuMDk2LTExLjg2OC03Ljk5NCA2LjE4LTEzLjQ3MnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZD0iTTcuMjkzIDE3LjM2OGExIDEgMCAwIDEgLjkyNS0xLjA3NGwxOS45ODYtMS40NS0yLjgyNyA2LjA5LTE3LjcwMyAxLjM4LS4zODEtNC45NDZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yNTg4IC4yNTg4IC4yNTg4KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjUuMTggNTAuMjcybDI1LjMyMy42MTZhMS4wNjcgMS4wNjcgMCAwIDAgMS4wOTMtMS4wNDJsLjgwNC0zMy4wODNhMS4wNjggMS4wNjggMCAwIDAtMS4wNDEtMS4wOTNsLTE1LjI2My0uMzctOS4yNDQgMTIuNTQgMTEuMzk2IDguMDlMMjUuMTggNTAuMjczeiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMjEgLjIxIC4yMSkiLz48cGF0aCBkPSJNMzYuMTE5IDE1LjMxbDE1LjMxNi4zNjhhMSAxIDAgMCAxIC45NzYgMS4wMjdsLS4xMzggNC45MTctMjAuMzgtLjY2MyA0LjIyNi01LjY0OHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjI1ODggLjI1ODggLjI1ODgpIi8+PGNpcmNsZSBjeD0iNDkuNSIgY3k9IjQ3IiByPSI3IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zOSAuMzkgLjM5KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNTAuMjA3IDUwLjgyNGgtMS40MTJ2LTEuNDExaDEuNDEydjEuNDExek01MC41IDQzbC0uMjM4IDUuMDUxaC0xLjUyNEw0OC41IDQzaDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZD0iTTM0LjQ0NSA3LjIwOGwtMS4yNDkgNS43MjhjLS4wNjMuMjg4LjMzMi40MzUuNDczLjE3N2wyLjc4Ny01LjA5NWEuMjU1LjI1NSAwIDAgMC0uMTI2LS4zNThsLTEuNTM5LS42MzRhLjI1NS4yNTUgMCAwIDAtLjM0Ni4xODJ6bS03LjgzMiAyLjU1NmwyLjYxNyAzLjMzYy4xNzguMjI3LjUzNy4wMy40NDEtLjI0MmwtMS4zODgtMy45NDVhLjI1NS4yNTUgMCAwIDAtLjM1NS0uMTQ0bC0xLjIyOS42MTVhLjI1NS4yNTUgMCAwIDAtLjA4Ni4zODZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC42NiAuNjYgLjY2KSIvPjwvc3ZnPg==);
  --icon-status-error-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0yOC4yMSAxNC44MDZMOC4yOSAxNi4yMjZhMS4wNjcgMS4wNjcgMCAwIDAtLjk4OCAxLjE0MmwyLjM1NSAzMy4wMDhjLjA0Mi41ODkuNTUyIDEuMDMxIDEuMTQuOTlsMTMuOTc1LS45OTggOS4xMjQtMTQuMDk2LTExLjg2OC03Ljk5NCA2LjE4LTEzLjQ3MnoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZD0iTTcuMjkzIDE3LjM2OGExIDEgMCAwIDEgLjkyNS0xLjA3NGwxOS45ODYtMS40NS0yLjgyNyA2LjA5LTE3LjcwMyAxLjM4LS4zODEtNC45NDZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4yNTg4IC4yNTg4IC4yNTg4KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjUuMTggNTAuMjcybDI1LjMyMy42MTZhMS4wNjcgMS4wNjcgMCAwIDAgMS4wOTMtMS4wNDJsLjgwNC0zMy4wODNhMS4wNjggMS4wNjggMCAwIDAtMS4wNDEtMS4wOTNsLTE1LjI2My0uMzctOS4yNDQgMTIuNTQgMTEuMzk2IDguMDlMMjUuMTggNTAuMjczeiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMjEgLjIxIC4yMSkiLz48cGF0aCBkPSJNMzYuMTE5IDE1LjMxbDE1LjMxNi4zNjhhMSAxIDAgMCAxIC45NzYgMS4wMjdsLS4xMzggNC45MTctMjAuMzgtLjY2MyA0LjIyNi01LjY0OHoiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjI1ODggLjI1ODggLjI1ODgpIi8+PGNpcmNsZSBjeD0iNDkuNSIgY3k9IjQ3IiByPSI3IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC4zOSAuMzkgLjM5KSIvPjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNTAuMjA3IDUwLjgyNGgtMS40MTJ2LTEuNDExaDEuNDEydjEuNDExek01MC41IDQzbC0uMjM4IDUuMDUxaC0xLjUyNEw0OC41IDQzaDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZD0iTTM0LjQ0NSA3LjIwOGwtMS4yNDkgNS43MjhjLS4wNjMuMjg4LjMzMi40MzUuNDczLjE3N2wyLjc4Ny01LjA5NWEuMjU1LjI1NSAwIDAgMC0uMTI2LS4zNThsLTEuNTM5LS42MzRhLjI1NS4yNTUgMCAwIDAtLjM0Ni4xODJ6bS03LjgzMiAyLjU1NmwyLjYxNyAzLjMzYy4xNzguMjI3LjUzNy4wMy40NDEtLjI0MmwtMS4zODgtMy45NDVhLjI1NS4yNTUgMCAwIDAtLjM1NS0uMTQ0bC0xLjIyOS42MTVhLjI1NS4yNTUgMCAwIDAtLjA4Ni4zODZ6IiBmaWxsPSJjb2xvcihkaXNwbGF5LXAzIC42NiAuNjYgLjY2KSIvPjwvc3ZnPg==);
  --icon-status-positive-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iOS41IiB5PSIxMC45MDYiIHdpZHRoPSIzOCIgaGVpZ2h0PSI0MCIgcng9IjEiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMzIgLjMyIC4zMikiIGQ9Ik0xNi41IDE5LjkwNmgyNHY0aC0yNHptMCA3aDEydjRoLTEyeiIvPjxwYXRoIGQ9Ik01My41IDYuOTA2bC4zMzIuODk2YTMgMyAwIDAgMCAxLjc3MiAxLjc3M2wuODk2LjMzMS0uODk2LjMzMmEzIDMgMCAwIDAtMS43NzIgMS43NzJsLS4zMzIuODk2LS4zMzItLjg5NmEzIDMgMCAwIDAtMS43NzItMS43NzJsLS44OTYtLjMzMi44OTYtLjMzMWEzIDMgMCAwIDAgMS43NzItMS43NzNsLjMzMi0uODk2eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuNjIgLjYyIC42MikiLz48cGF0aCBkPSJNNDUuNSAzMS4zMTNsNC4wNjEgNC4xOTUgNS44MzktLjA5NS0uMDk2IDUuODM5IDQuMTk2IDQuMDYtNC4xOTYgNC4wNjIuMDk2IDUuODM4LTUuODM5LS4wOTUtNC4wNjEgNC4xOTYtNC4wNjEtNC4xOTYtNS44MzkuMDk1LjA5Ni01LjgzOS00LjE5Ni00LjA2IDQuMTk2LTQuMDYyLS4wOTYtNS44MzggNS44MzkuMDk1IDQuMDYxLTQuMTk2eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMzkgLjM5IC4zOSkiLz48cGF0aCBkPSJNNDEuNSA0NC4wNTFsMy4yMTYgMy4wNjUgNS40NjctNS4yMSIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjEuNjc5Ii8+PC9zdmc+);
  --icon-status-positive-s-v2: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iOS41IiB5PSIxMC45MDYiIHdpZHRoPSIzOCIgaGVpZ2h0PSI0MCIgcng9IjEiIGZpbGw9ImNvbG9yKGRpc3BsYXktcDMgLjIxIC4yMSAuMjEpIi8+PHBhdGggZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMzIgLjMyIC4zMikiIGQ9Ik0xNi41IDE5LjkwNmgyNHY0aC0yNHptMCA3aDEydjRoLTEyeiIvPjxwYXRoIGQ9Ik01My41IDYuOTA2bC4zMzIuODk2YTMgMyAwIDAgMCAxLjc3MiAxLjc3M2wuODk2LjMzMS0uODk2LjMzMmEzIDMgMCAwIDAtMS43NzIgMS43NzJsLS4zMzIuODk2LS4zMzItLjg5NmEzIDMgMCAwIDAtMS43NzItMS43NzJsLS44OTYtLjMzMi44OTYtLjMzMWEzIDMgMCAwIDAgMS43NzItMS43NzNsLjMzMi0uODk2eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuNjIgLjYyIC42MikiLz48cGF0aCBkPSJNNDUuNSAzMS4zMTNsNC4wNjEgNC4xOTUgNS44MzktLjA5NS0uMDk2IDUuODM5IDQuMTk2IDQuMDYtNC4xOTYgNC4wNjIuMDk2IDUuODM4LTUuODM5LS4wOTUtNC4wNjEgNC4xOTYtNC4wNjEtNC4xOTYtNS44MzkuMDk1LjA5Ni01LjgzOS00LjE5Ni00LjA2IDQuMTk2LTQuMDYyLS4wOTYtNS44MzggNS44MzkuMDk1IDQuMDYxLTQuMTk2eiIgZmlsbD0iY29sb3IoZGlzcGxheS1wMyAuMzkgLjM5IC4zOSkiLz48cGF0aCBkPSJNNDEuNSA0NC4wNTFsMy4yMTYgMy4wNjUgNS40NjctNS4yMSIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjEuNjc5Ii8+PC9zdmc+);
  --button-color-bg-solid-default-brand: var(--tea-color-bg-brand-default);
  --button-color-bg-solid-hover-brand: var(--tea-color-bg-brand-hover);
  --button-color-bg-solid-active-brand: var(--tea-color-bg-brand-active);
  --button-color-bg-solid-focus-brand: var(--tea-color-bg-brand-focus);
  --button-color-bg-solid-disabled-brand: var(--tea-color-bg-brand-disabled);
  --button-color-border-solid-default-brand: var(--tea-color-border-on-bg-brand-default);
  --button-color-border-solid-hover-brand: var(--tea-color-border-on-bg-brand-hover);
  --button-color-border-solid-active-brand: var(--tea-color-border-on-bg-brand-active);
  --button-color-border-solid-focus-brand: var(--tea-color-border-on-bg-brand-focus);
  --button-color-border-solid-disabled-brand: var(--tea-color-border-on-bg-brand-disabled);
  --button-color-text-solid-default-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-hover-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-active-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-focus-brand: var(--tea-color-text-on-bg-brand-default);
  --button-color-text-solid-disabled-brand: var(--tea-color-text-on-bg-brand-disabled);
  --button-color-bg-solid-default-warning: var(--tea-color-bg-warning-default);
  --button-color-bg-solid-hover-warning: var(--tea-color-bg-warning-hover);
  --button-color-bg-solid-active-warning: var(--tea-color-bg-warning-active);
  --button-color-bg-solid-focus-warning: var(--tea-color-bg-warning-focus);
  --button-color-bg-solid-disabled-warning: var(--tea-color-bg-warning-disabled);
  --button-color-border-solid-default-warning: var(--tea-color-border-on-bg-warning-default);
  --button-color-border-solid-hover-warning: var(--tea-color-border-on-bg-warning-hover);
  --button-color-border-solid-active-warning: var(--tea-color-border-on-bg-warning-active);
  --button-color-border-solid-focus-warning: var(--tea-color-border-on-bg-warning-focus);
  --button-color-border-solid-disabled-warning: var(--tea-color-border-on-bg-warning-disabled);
  --button-color-text-solid-default-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-hover-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-active-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-focus-warning: var(--tea-color-text-on-bg-warning-default);
  --button-color-text-solid-disabled-warning: var(--tea-color-text-on-bg-warning-disabled);
  --button-color-bg-solid-default-error: var(--tea-color-bg-error-default);
  --button-color-bg-solid-hover-error: var(--tea-color-bg-error-hover);
  --button-color-bg-solid-active-error: var(--tea-color-bg-error-active);
  --button-color-bg-solid-focus-error: var(--tea-color-bg-error-focus);
  --button-color-bg-solid-disabled-error: var(--tea-color-bg-error-disabled);
  --button-color-border-solid-default-error: var(--tea-color-border-on-bg-error-default);
  --button-color-border-solid-hover-error: var(--tea-color-border-on-bg-error-hover);
  --button-color-border-solid-active-error: var(--tea-color-border-on-bg-error-active);
  --button-color-border-solid-focus-error: var(--tea-color-border-on-bg-error-focus);
  --button-color-border-solid-disabled-error: var(--tea-color-border-on-bg-error-disabled);
  --button-color-text-solid-default-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-hover-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-active-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-focus-error: var(--tea-color-text-on-bg-error-default);
  --button-color-text-solid-disabled-error: var(--tea-color-text-on-bg-error-disabled);
  --button-color-bg-outline-default-neutral: var(--tea-color-bg-primary-default);
  --button-color-bg-outline-hover-neutral: var(--tea-color-bg-primary-hover);
  --button-color-bg-outline-active-neutral: var(--tea-color-bg-primary-active);
  --button-color-bg-outline-focus-neutral: var(--tea-color-bg-primary-default);
  --button-color-bg-outline-disabled-neutral: var(--tea-color-bg-primary-default);
  --button-color-border-outline-default-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-hover-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-active-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-focus-neutral: var(--tea-color-border-primary-default);
  --button-color-border-outline-disabled-neutral: var(--tea-color-border-primary-disabled);
  --button-color-text-outline-default-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-hover-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-active-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-focus-neutral: var(--tea-color-text-primary);
  --button-color-text-outline-disabled-neutral: var(--tea-color-text-disabled);
  --button-size-height-sm: 24px;
  --button-size-height-md: var(--tea-form-height-default);
  --button-size-height-lg: 40px;
  --button-font-size-sm: var(--tea-font-size-default);
  --button-font-size-md: var(--tea-font-size-default);
  --button-font-size-lg: var(--tea-font-size-350);
  --button-shadow-outlined-neutral: 0px 2px 0px 0px hsla(0,0%,73.3%,.05);
  --button-shadow-solid-brand: 0px 2px 0px 0px rgba(var(--tea-color-palette-blue-rgb),.05);
  --button-shadow-solid-error: 0px 2px 0px 0px rgba(var(--tea-color-palette-red-rgb),.05);
  --button-shadow-solid-warning: 0px 2px 0px 0px rgba(var(--tea-color-palette-orange-rgb),.05);
  --button-color-bg-solid-default-neutral: var(--tea-color-palette-gray-14);
  --button-color-bg-solid-hover-neutral: var(--tea-color-palette-gray-11);
  --button-color-bg-solid-active-neutral: var(--tea-color-palette-gray-14);
  --button-color-bg-solid-focus-neutral: var(--tea-color-palette-gray-11);
  --button-color-bg-solid-disabled-neutral: var(--tea-color-palette-gray-11);
  --button-color-text-solid-default-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-hover-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-active-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-focus-neutral: var(--tea-color-text-on-bg-inverse-default);
  --button-color-text-solid-disabled-neutral: var(--tea-color-text-on-bg-inverse-disabled,var(--tea-color-palette-white-70));
  --button-shadow-outlined-neutral: 0px 2px 0px 0px var(--tea-color-palette-black-10);
  --button-shadow-solid-brand: 0px 2px 0px 0px var(--tea-color-palette-black-10);
  --button-shadow-solid-error: 0px 2px 0px 0px var(--tea-color-palette-black-10);
  --button-shadow-solid-warning: 0px 2px 0px 0px var(--tea-color-palette-black-10);
  --button-color-bg-solid-default-neutral: var(--tea-color-palette-bluegray-1);
  --button-color-bg-solid-hover-neutral: var(--tea-color-palette-white-100);
  --button-color-bg-solid-active-neutral: var(--tea-color-palette-bluegray-1);
  --button-color-bg-solid-focus-neutral: var(--tea-color-palette-white-100);
  --button-color-bg-solid-disabled-neutral: var(--tea-color-palette-bluegray-2);
  --button-color-text-solid-disabled-neutral: var(--tea-color-text-on-bg-inverse-disabled,var(--tea-color-palette-black-30));
  --tag-font-size: var(--tea-font-size-default);
  --tag-line-height: 18px;
  --tag-group-gap: 4px;
  --tag-border-radius: calc((var(--tag-line-height) + 2px)/2);
  --tag-icon-dismiss-right: calc(var(--tea-space-100)*2 - 1px);
  --tag-padding: 0 7px 0 7px;
  --tag-edit-padding: 0 25px 0 10px;
  --tag-color-border-default: var(--tea-color-border-tertiary-default);
  --tag-color-border-brand: var(--tea-color-border-on-bg-brand-lighten-default);
  --tag-color-border-success: var(--tea-color-border-on-bg-success-lighten-default);
  --tag-color-border-warning: var(--tea-color-border-on-bg-warning-lighten-default);
  --tag-color-border-error: var(--tea-color-border-on-bg-error-lighten-default);
  --tag-color-text-default: var(--tea-color-text-tertiary);
  --tag-color-text-hightlight: var(--tea-color-text-on-bg-inverse-default);
  --tag-color-text-brand: var(--tea-color-text-on-bg-brand-lighten-default);
  --tag-color-text-success: var(--tea-color-text-on-bg-success-lighten-default);
  --tag-color-text-warning: var(--tea-color-text-on-bg-warning-lighten-default);
  --tag-color-text-error: var(--tea-color-text-on-bg-error-lighten-default);
  --tag-color-bg-bright-default: var(--tea-color-bg-tertiary-default);
  --tag-color-bg-bright-brand: var(--tea-color-bg-brand-lighten-default);
  --tag-color-bg-bright-success: var(--tea-color-bg-success-lighten-default);
  --tag-color-bg-bright-warning: var(--tea-color-bg-warning-lighten-default);
  --tag-color-bg-bright-error: var(--tea-color-bg-error-lighten-default);
  --tag-color-bg-darkness-default: var(--tea-color-bg-inverse-default);
  --tag-color-bg-darkness-brand: var(--tea-color-bg-brand-default);
  --tag-color-bg-darkness-success: var(--tea-color-bg-success-default);
  --tag-color-bg-darkness-warning: var(--tea-color-bg-warning-default);
  --tag-color-bg-darkness-error: var(--tea-color-bg-error-default);
  --tag-input-color-bg: var(--tea-form-color-bg-primary-default);
  --tag-input-color-border: var(--tea-color-border-primary-default);
  --list-item-color-bg-selected: var(--tea-color-bg-brand-default);
  --list-item-color-text-selected: var(--tea-color-text-on-bg-brand-default);
  --range-datepicker-container-width-format-1: 220px;
  --range-datepicker-container-width-format-2: 325px;
  --range-datepicker-container-width-format-3: 360px;
  --range-datepicker-cell-in-range-color-bg-default: var(--tea-color-bg-brand-lighten-default);
  --switch-dot-color-bg: var(--tea-color-bg-primary-default);
  --switch-color-bg-default: var(--tea-color-palette-bluegray-4);
  --switch-color-bg-hover: var(--tea-color-palette-bluegray-5);
  --switch-color-bg-disabled: var(--tea-color-palette-bluegray-4);
  --switch-color-bg-default: var(--tea-color-palette-gray-9);
  --switch-color-bg-hover: var(--tea-color-palette-gray-8);
  --switch-color-bg-disabled: var(--tea-color-palette-gray-10);
  --bubbles-font-size: var(--tea-font-size-default);
  --bubbles-border-radius: var(--tea-border-radius-default);
  --bubbles-width-max: 300px;
  --bubbles-triangle-width: 6px;
  --bubbles-border-width: 1px;
  --bubbles-color-bg-inverse: var(--tea-color-bg-inverse-default);
  --bubbles-color-bg-default: var(--tea-color-bg-primary-default);
  --bubbles-color-bg-error: var(--tea-color-bg-error-lighten-default);
  --bubbles-color-text-inverse: var(--tea-color-text-on-bg-inverse-default);
  --bubbles-color-text-default: var(--tea-color-text-primary);
  --bubbles-color-text-error: var(--tea-color-text-on-bg-error-lighten-default);
  --bubbles-color-border-inverse: var(--tea-color-bg-inverse-default);
  --bubbles-color-border-default: var(--tea-color-bg-primary-default);
  --bubbles-color-border-error: var(--tea-color-bg-error-lighten-default);
  --bubbles-color-border-default: var(--tea-color-border-primary-default);
  --card-box-shadow-default: var(--tea-shadow-xs);
  --card-box-shadow-hover: var(--tea-shadow-sm);
  --card-size-border-default: 0px;
  --card-color-border-default: var(--card-color-bg);
  --card-bordered-color-border-default: var(--tea-color-border-primary-default);
  --card-color-text: var(--tea-color-text-primary);
  --card-color-text-maintitle: var(--tea-color-text-primary);
  --card-border-radius: var(--tea-border-radius-default);
  --card-color-borde-inner: var(--tea-color-border-secondary-default);
  --card-title-margin-bottom: 18px;
  --card-multiple-margin-top: 20px;
  --card-font-size: var(--tea-font-size-default);
  --card-body-title-font-size: var(--tea-font-size-400);
  --card-body-padding: 24px 32px;
  --card-header-padding: 20px 32px;
  --card-color-bg: var(--tea-color-bg-primary-default);
  --card-size-border-default: 1px;
  --card-color-border-default: var(--tea-color-border-primary-default);
  --card-color-text-subtitle: var(--tea-color-text-tertiary);
  --dropdown-color-bg: var(--tea-form-color-bg-primary-default);
  --tooltips-border-radius: var(--tea-border-radius-default);
  --tooltips-color-bg-default: var(--tea-color-bg-inverse-default);
  --tooltips-color-border-default: var(--tea-color-bg-inverse-default);
  --tooltips-color-text-default: var(--tea-color-text-on-bg-inverse-default);
  --tooltips-color-bg-inverse: var(--tea-color-bg-primary-default);
  --tooltips-color-border-inverse: var(--tea-color-bg-primary-default);
  --tooltips-color-text-inverse: var(--tea-color-text-primary);
  --rate-color-bg-default: var(--tea-color-palette-bluegray-4);
  --rate-color-bg-default: var(--tea-color-palette-gray-9);
  --alert-nav-color-bg: var(--tea-color-bg-primary-default);
  --alert-primary-color-bg: var(--tea-color-bg-brand-lighten-default);
  --alert-primary-color-border: var(--tea-color-bg-brand-lighten-default);
  --alert-primary-color-text: var(--tea-color-text-on-bg-brand-lighten-default);
  --alert-primary-color-icon: var(--tea-color-text-on-bg-brand-lighten-default);
  --alert-error-color-bg: var(--tea-color-bg-error-lighten-default);
  --alert-error-color-border: var(--tea-color-bg-error-lighten-default);
  --alert-error-color-text: var(--tea-color-text-on-bg-error-lighten-default);
  --alert-error-color-icon: var(--tea-color-text-on-bg-error-lighten-default);
  --alert-warning-color-bg: var(--tea-color-bg-warning-lighten-default);
  --alert-warning-color-border: var(--tea-color-bg-warning-lighten-default);
  --alert-warning-color-text: var(--tea-color-text-on-bg-warning-lighten-default);
  --alert-warning-color-icon: var(--tea-color-text-on-bg-warning-lighten-default);
  --alert-success-color-bg: var(--tea-color-bg-success-lighten-default);
  --alert-success-color-border: var(--tea-color-bg-success-lighten-default);
  --alert-success-color-text: var(--tea-color-text-on-bg-success-lighten-default);
  --alert-success-color-icon: var(--tea-color-text-on-bg-success-lighten-default);
  --alert-notice-color-bg: var(--tea-color-bg-primary-default);
  --alert-notice-color-bg-icon: var(--tea-color-palette-gray-1);
  --alert-notice-color-text: var(--tea-color-text-primary);
  --alert-notice-color-border: var(--tea-color-bg-primary-default);
  --segment-button-color-bg-default: var(--tea-form-color-bg-primary-default);
  --segment-button-color-bg-hover: var(--tea-form-color-bg-primary-hover);
  --segment-button-color-bg-selected: var(--tea-form-color-bg-primary-default);
  --segment-button-color-bg-disabled: var(--tea-form-color-bg-primary-disabled);
  --segment-button-color-border-default: var(--tea-color-border-primary-default);
  --segment-button-color-border-hover: var(--tea-color-border-primary-default);
  --segment-button-color-border-selected: var(--tea-color-border-brand-default);
  --segment-button-color-border-disabled: var(--tea-color-border-primary-default);
  --segment-button-color-text-default: var(--tea-color-text-primary);
  --segment-button-color-text-hover: var(--tea-color-text-primary);
  --segment-button-color-text-selected: var(--tea-color-text-brand-default);
  --segment-button-color-text-disabled: var(--tea-color-text-disabled);
  --step-num-color-bg-default: var(--tea-color-bg-brand-default);
  --step-num-color-bg-disabled: var(--tea-form-color-bg-primary-default);
  --step-num-color-border-default: var(--tea-color-border-on-bg-brand-default);
  --step-num-color-border-disabled: var(--tea-color-border-primary-default);
  --step-num-color-text-default: var(--tea-color-text-on-bg-brand-default);
  --step-num-color-text-disabled: var(--tea-color-text-disabled);
  --step-arrow-color-bg-default: var(--tea-color-border-primary-default);
  --step-num-color-bg-default-process: var(--tea-color-bg-brand-lighten-default);
  --step-num-color-bg-disabled-process: var(--tea-form-color-bg-primary-default);
  --step-num-color-border-default-process: var(--tea-color-bg-brand-lighten-default);
  --step-num-color-border-disabled-process: var(--tea-color-border-primary-default);
  --step-num-color-text-default-process: var(--tea-color-text-on-bg-brand-lighten-default);
  --step-num-color-text-disabled-process: var(--tea-color-text-on-bg-brand-lighten-default);
  --step-arrow-color-bg-default-process: var(--tea-color-border-primary-default);
  --tabs-color-text-default: var(--tea-color-text-secondary);
  --tabs-color-text-active: var(--tea-color-text-primary);
  --tabs-color-text-disabled: var(--tea-color-text-disabled);
  --tabs-color-border-default: var(--tea-color-border-primary-default);
  --tabs-color-border-active: var(--tea-color-border-brand-default);
  --tabs-height-default: var(--tea-form-height-default);
  --tabs-animation: .15s ease-in-out,width .15s ease-in-out,height .15s ease-in-out;
  --tabs-space-horizontal-outer: 16px;
  --tabs-item-color-bg-segment-default: var(--tea-color-bg-tertiary-default);
  --tabs-item-color-bg-segment-hover: var(--tea-color-bg-tertiary-default);
  --tabs-item-color-bg-segment-active: var(--tea-color-bg-primary-default);
  --tabs-item-color-bg-segment-default: #101010;
  --tabs-item-color-bg-segment-active: var(--tea-color-bg-tertiary-default);
  --tabs-item-color-bg-segment-hover: var(--tea-color-bg-primary-default);
  --table-color-bg-primary-default: var(--tea-color-bg-primary-default);
  --table-color-border-primary-default: var(--tea-color-border-primary-default);
  --table-color-border-secondary-default: var(--tea-color-border-secondary-default);
  --table-header-color-bg-primary-default: var(--tea-color-bg-primary-default);
  --table-header-color-bg-gray-default: var(--tea-color-bg-primary-lighten);
  --table-header-color-text-default: var(--tea-color-text-secondary);
  --table-header-font-weight: var(--tea-font-weight-medium);
  --table-cell-color-bg-hover: var(--tea-color-bg-primary-hover);
  --table-color-bg-primary-hover: var(--tea-color-bg-primary-hover);
  --table-color-bg-secondary-default: var(--tea-color-bg-secondary-default);
  --table-fake-bar-color-bg-default: var(--tea-color-palette-black-10);
  --table-fake-bar-color-bg-hover: var(--tea-color-palette-black-20);
  --table-nesting-color-bg-default: var(--tea-color-bg-primary-lighten);
  --table-cell-space-horizontal-md: 10px;
  --table-cell-space-horizontal-sm: 10px;
  --table-cell-space-vertical-md: 16px;
  --table-cell-space-vertical-sm: 12px;
  --table-action-panel-space-vertical: 16px;
  --table-color-mask-default: var(--tea-color-palette-white-80);
  --table-animation-duration: .1s;
  --table-fake-bar-color-bg-default: var(--tea-color-palette-white-20);
  --table-fake-bar-color-bg-hover: var(--tea-color-palette-white-40);
  --table-nesting-color-bg-default: var(--tea-color-bg-page-default);
  --table-color-mask-default: var(--tea-color-palette-black-80);
  --menu-padding-both-sides: 16px;
  --menu-bg: var(--tea-color-bg-primary-default);
  --menu-border: var(--tea-color-border-secondary-default);
  --menu-text-default: var(--tea-color-text-primary);
  --ment-text-secondary: var(--tea-color-text-secondary);
  --menu-text-label: var(--tea-color-text-tertiary);
  --menu-item-bg-hover: var(--tea-color-bg-primary-hover);
  --menu-item-text-hover: var(--tea-color-text-primary);
  --menu-item-bg-active: var(--tea-color-bg-brand-default);
  --menu-item-text-active: var(--tea-color-text-on-bg-brand-default);
  --menu-badge-bg-default: var(--tea-color-bg-secondary-hover);
  --menu-badge-text-default: var(--tea-color-text-tertiary);
  --menu-icon-color: var(--tea-color-text-tertiary);
  --menu-icon-color-active: var(--tea-color-text-on-bg-brand-default);
  --menu-icon-jump-color-text: var(--tea-color-text-brand-default);
  --menu-icon-back-color-text: var(--tea-color-text-brand-default);
  --menu-item-bg-hover: var(--tea-color-bg-secondary-hover);
  --blank-color-bg: var(--tea-color-bg-primary-default);
  --notification-size-width-max: 368px;
}

@media(max-width:1024px) {
  .tea-theme-dark,
  [theme-mode=dark][theme-enable=true] {
    --card-body-padding: 16px;
    --card-header-padding: 16px;
  }
}

