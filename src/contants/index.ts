import { MessageTypeEnum, ContentType } from '@src/types';

export enum UrlParamEnum {
  plugin='plugin',
  archId = 'archId',
  taskId = 'taskId',
  region = 'region',
  newTaskId = 'newTaskId',
  optype= 'optype',
  openDirectory = 'openDirectory',
  createArch = 'createArch',
  pluginParam = 'pluginParam',
  appid = 'appid',
}

export const BOTTOM_ITEM_ID = 'bottom-item';

export const SQL_SIGN = '__sql__';
export const TITLE_SIGN = '__title__';
export const CHART_TYPE_SIGN = '__chartType__';

export const MODEL_OPTIONS = [
  {
    text: 'DeepSeek通道一',
    value: 'channel1',
  },
  {
    text: 'DeepSeek通道二',
    value: 'channel2',
  },
];

export const NO_DATA_TIP = '当前条件查询暂无数据，建议修改查询条件重新查询';

export const DEFAULT_QUESTION = '了解我能做什么';
export const DEFAULT_TIP = `你好！我是云顾问BI助手。

我的专长是：

* 针对左边“架构图”绑定的云产品/资源，接收你对各种运维指标（如CPU使用率、内存使用率等）的查询、统计需求，绘制折线、柱状、饼图等图表

你可以：
* 阅读下表了解我已支持的产品类型和指标项
* 或在下方对话框中用自然语言描述你的统计需求
* 或借助对话框上侧的模板（如选查询范围、选指标等）细化需求描述`;
export const DEFAULT_MESSAGE = [{
  type: MessageTypeEnum.bot,
  contentType: ContentType.text,
  questions: [DEFAULT_QUESTION],
  contentList: [DEFAULT_TIP],
}];

export const MESSAGE_CONTAINER_ID = 'advisor-message-container';

export enum DashboardItemTypeEnum {
  sql = 'sql-graph',
  text = 'text-graph',
}

export enum DashboardItemValid {
  valid = 'valid',
  invalid = 'invalid',
}

export const CHANGE_INPUT_TIP = '例如：修改标题为xxx；改为柱状图；改为科技风格等';

export const NEED_GET_TABLE_SIGN = '<!--indicator-->';

export const INPUT_MAX_LENGTH = 512;

export const MAX_CHART_COUNT = 50;
export const MAX_CHART_TIP = `最多支持${MAX_CHART_COUNT}个图表和文本，请删除后重试`;

export const DRAWER_ID = 'chatBiSdkDrawer';

export enum MessageEventType {
  governTopicThinking = 'govern-topic-thinking',
  governTopicMessage = 'govern-topic-message'
}

export enum MessageEventStatus {
  start = 'start',
  inProgress = 'in_progress',
  end = 'end'
}
