export interface VersionType {
  fileName: string;
  updateTime: string;
  vId: number;
  vName: string;
  archId: string;
  appId: number,
  uin: string,
  username: string;
  archName: string;
  folderId: number;
  desc: string;
  isLatest: boolean;
}

export enum MessageTypeEnum {
  user = 'user',
  bot = 'bot',
  insights = 'insights',
}

export enum ContentType {
  text = 'text',
  image = 'image',
  iframeUrl = 'iframeUrl',
  chart = 'chart',
  default = 'default'
}

export interface MessageType {
  id?: string;
  type: MessageTypeEnum,
  contentType: ContentType;
  questions?: string[];
  reasoningContent?: string;
  requestId?: string;
  isInsight?: boolean;
  contentList?: string[];
  status?: 'loading' | 'finished' | 'normal' | 'error',
  loadingText?: string;
  errorText?: string;
  currentNodeKey?: string; // 当前问题选择的node
  event?: string;
  eventStatus?: string;
  thinkingStop?: boolean; // 思考是否被停止
  // eslint-disable-next-line camelcase
  extra?: { topic_id: string, msg: string };
}
